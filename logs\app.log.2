2025-06-03 16:41:59,182 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:143]
2025-06-03 16:42:24,002 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:143]
2025-06-03 16:46:11,560 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:143]
2025-06-03 16:46:15,654 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:143]
2025-06-03 16:46:18,907 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:143]
2025-06-03 16:47:39,013 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:786]
2025-06-03 16:48:21,793 WARNING: Suspicious method blocked: HEAD from 14.103.175.118 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:120]
2025-06-03 16:48:21,795 ERROR: Security middleware error: 405 Method Not Allowed: The method is not allowed for the requested URL. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:161]
2025-06-03 16:48:52,016 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:143]
2025-06-03 16:55:41,238 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:143]
2025-06-03 16:55:44,391 INFO: 查看库存详情: ID=75, 批次号=B20250603b9b7cb [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:415]
2025-06-03 16:55:54,274 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:143]
2025-06-03 16:55:56,578 INFO: 查看库存详情: ID=75, 批次号=B20250603b9b7cb [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:415]
2025-06-03 17:00:01,328 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-03 17:00:01,426 INFO: 成功获取 0 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-03 17:00:06,194 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:143]
2025-06-03 17:00:23,046 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:786]
2025-06-03 17:01:03,800 WARNING: Suspicious method blocked: HEAD from 14.103.175.118 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:120]
2025-06-03 17:01:03,800 ERROR: Security middleware error: 405 Method Not Allowed: The method is not allowed for the requested URL. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:161]
2025-06-03 17:01:16,730 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:143]
2025-06-03 17:01:21,464 ERROR: 库存统计查询失败: (pyodbc.ProgrammingError) ('42000', "[42000] [Microsoft][ODBC SQL Server Driver][SQL Server]'DATE' 不是可以识别的 内置函数名称。 (195) (SQLExecDirectW); [42000] [Microsoft][ODBC SQL Server Driver][SQL Server]无法预定义语句。 (8180)")
[SQL: 
        SELECT
            DATE(si.stock_in_date) as date,
            COUNT(DISTINCT si.id) as stock_in_count,
            COUNT(sii.id) as stock_in_items,
            SUM(sii.quantity * sii.unit_price) as stock_in_amount,
            SUM(sii.quantity) as stock_in_quantity
        FROM stock_ins si
        JOIN stock_in_items sii ON si.id = sii.stock_in_id
        JOIN warehouses w ON si.warehouse_id = w.id
        JOIN ingredients i ON sii.ingredient_id = i.id
        WHERE w.area_id = 42 AND DATE(si.stock_in_date) >= ? AND DATE(si.stock_in_date) <= ?
        GROUP BY DATE(si.stock_in_date)
        ORDER BY DATE(si.stock_in_date)
    ]
[parameters: ('2025-05-04', '2025-06-03')]
(Background on this error at: https://sqlalche.me/e/20/f405) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:942]
2025-06-03 17:01:39,970 ERROR: 库存统计查询失败: (pyodbc.ProgrammingError) ('42000', "[42000] [Microsoft][ODBC SQL Server Driver][SQL Server]'DATE' 不是可以识别的 内置函数名称。 (195) (SQLExecDirectW); [42000] [Microsoft][ODBC SQL Server Driver][SQL Server]无法预定义语句。 (8180)")
[SQL: 
        SELECT
            DATE(si.stock_in_date) as date,
            COUNT(DISTINCT si.id) as stock_in_count,
            COUNT(sii.id) as stock_in_items,
            SUM(sii.quantity * sii.unit_price) as stock_in_amount,
            SUM(sii.quantity) as stock_in_quantity
        FROM stock_ins si
        JOIN stock_in_items sii ON si.id = sii.stock_in_id
        JOIN warehouses w ON si.warehouse_id = w.id
        JOIN ingredients i ON sii.ingredient_id = i.id
        WHERE w.area_id = 42 AND DATE(si.stock_in_date) >= ? AND DATE(si.stock_in_date) <= ? AND si.warehouse_id = ? AND i.category_id = ?
        GROUP BY DATE(si.stock_in_date)
        ORDER BY DATE(si.stock_in_date)
    ]
[parameters: ('2025-05-04', '2025-06-03', 4, 1)]
(Background on this error at: https://sqlalche.me/e/20/f405) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:942]
2025-06-03 17:05:23,258 ERROR: 库存统计查询失败: (pyodbc.ProgrammingError) ('42000', "[42000] [Microsoft][ODBC SQL Server Driver][SQL Server]'DATE' 不是可以识别的 内置函数名称。 (195) (SQLExecDirectW); [42000] [Microsoft][ODBC SQL Server Driver][SQL Server]无法预定义语句。 (8180)")
[SQL: 
        SELECT
            DATE(si.stock_in_date) as date,
            COUNT(DISTINCT si.id) as stock_in_count,
            COUNT(sii.id) as stock_in_items,
            SUM(sii.quantity * sii.unit_price) as stock_in_amount,
            SUM(sii.quantity) as stock_in_quantity
        FROM stock_ins si
        JOIN stock_in_items sii ON si.id = sii.stock_in_id
        JOIN warehouses w ON si.warehouse_id = w.id
        JOIN ingredients i ON sii.ingredient_id = i.id
        WHERE w.area_id = 42 AND DATE(si.stock_in_date) >= ? AND DATE(si.stock_in_date) <= ?
        GROUP BY DATE(si.stock_in_date)
        ORDER BY DATE(si.stock_in_date)
    ]
[parameters: ('2025-05-04', '2025-06-03')]
(Background on this error at: https://sqlalche.me/e/20/f405) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:942]
2025-06-03 17:10:08,156 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:786]
2025-06-03 17:10:44,495 WARNING: Suspicious method blocked: HEAD from 14.103.175.118 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:120]
2025-06-03 17:10:44,496 ERROR: Security middleware error: 405 Method Not Allowed: The method is not allowed for the requested URL. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:161]
2025-06-03 17:14:58,900 WARNING: Suspicious method blocked: HEAD from 61.129.155.195 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:120]
2025-06-03 17:14:58,900 ERROR: Security middleware error: 405 Method Not Allowed: The method is not allowed for the requested URL. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:161]
2025-06-03 17:20:23,494 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-03 17:20:23,592 INFO: 成功获取 0 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-03 17:20:27,437 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:143]
2025-06-03 17:20:30,228 ERROR: 库存统计查询失败: (pyodbc.ProgrammingError) ('42000', "[42000] [Microsoft][ODBC SQL Server Driver][SQL Server]'DATE' 不是可以识别的 内置函数名称。 (195) (SQLExecDirectW); [42000] [Microsoft][ODBC SQL Server Driver][SQL Server]无法预定义语句。 (8180)")
[SQL: 
        SELECT
            DATE(si.stock_in_date) as date,
            COUNT(DISTINCT si.id) as stock_in_count,
            COUNT(sii.id) as stock_in_items,
            SUM(sii.quantity * sii.unit_price) as stock_in_amount,
            SUM(sii.quantity) as stock_in_quantity
        FROM stock_ins si
        JOIN stock_in_items sii ON si.id = sii.stock_in_id
        JOIN warehouses w ON si.warehouse_id = w.id
        JOIN ingredients i ON sii.ingredient_id = i.id
        WHERE w.area_id = 42 AND DATE(si.stock_in_date) >= ? AND DATE(si.stock_in_date) <= ?
        GROUP BY DATE(si.stock_in_date)
        ORDER BY DATE(si.stock_in_date)
    ]
[parameters: ('2025-05-04', '2025-06-03')]
(Background on this error at: https://sqlalche.me/e/20/f405) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:948]
2025-06-03 17:20:53,383 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:786]
2025-06-03 17:22:55,235 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:786]
