2025-06-03 14:35:12,998 INFO: SQL查询成功，找到菜单: id=37 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:70]
2025-06-03 14:35:13,004 INFO: 通过ID获取完整菜单对象成功: id=37 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:75]
2025-06-03 14:35:13,005 INFO: 获取周菜单: area_id=42, week_start=2025-05-26, 类型=<class 'datetime.date'> [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:33]
2025-06-03 14:35:13,006 INFO: 使用日期字符串: 2025-05-26 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:51]
2025-06-03 14:35:13,006 INFO: 执行SQL: 
                SELECT TOP 1 id, area_id, week_start, week_end, status, created_by, created_at, updated_at
                FROM weekly_menus
                WHERE area_id = :area_id AND CONVERT(VARCHAR(10), week_start, 120) = :week_start_str
                 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:63]
2025-06-03 14:35:13,007 INFO: SQL参数: area_id=42, week_start_str=2025-05-26 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:64]
2025-06-03 14:35:13,008 INFO: SQL查询成功，找到菜单: id=36 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:70]
2025-06-03 14:35:13,009 INFO: 通过ID获取完整菜单对象成功: id=36 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:75]
2025-06-03 14:35:13,010 INFO: 获取周菜单: area_id=42, week_start=2025-06-09, 类型=<class 'datetime.date'> [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:33]
2025-06-03 14:35:13,010 INFO: 使用日期字符串: 2025-06-09 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:51]
2025-06-03 14:35:13,010 INFO: 执行SQL: 
                SELECT TOP 1 id, area_id, week_start, week_end, status, created_by, created_at, updated_at
                FROM weekly_menus
                WHERE area_id = :area_id AND CONVERT(VARCHAR(10), week_start, 120) = :week_start_str
                 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:63]
2025-06-03 14:35:13,011 INFO: SQL参数: area_id=42, week_start_str=2025-06-09 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:64]
2025-06-03 14:35:13,011 INFO: SQL查询成功，找到菜单: id=38 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:70]
2025-06-03 14:35:13,012 INFO: 通过ID获取完整菜单对象成功: id=38 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:75]
2025-06-03 14:36:35,143 INFO: 当前用户: 18373062333 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:94]
2025-06-03 14:36:35,144 INFO: 用户区域ID: 42 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:95]
2025-06-03 14:36:35,148 INFO: 用户区域名称: 朝阳区实验中学 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:96]
2025-06-03 14:36:35,148 INFO: 是否管理员: 0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:97]
2025-06-03 14:36:57,660 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:143]
2025-06-03 14:37:03,059 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:143]
2025-06-03 14:37:05,295 INFO: 库存汇总查询条件: 状态=正常, 最小数量=0.001 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:281]
2025-06-03 14:37:08,893 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:143]
2025-06-03 14:37:22,638 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:143]
2025-06-03 14:37:28,475 INFO: 查看库存详情: ID=75, 批次号=B20250603b9b7cb [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:415]
2025-06-03 14:37:56,885 INFO: 使用消耗计划的区域ID: 42 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:217]
2025-06-03 14:37:56,885 INFO: 通过消耗计划信息读取菜谱：日期=2025-06-03, 餐次=早餐+午餐+晚餐, 区域ID=42 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:227]
2025-06-03 14:37:56,886 INFO: 查询周菜单：日期=2025-06-03, 星期=1(0=周一), day_of_week=2, 餐次=早餐+午餐+晚餐, 区域ID=42 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:242]
2025-06-03 14:37:56,891 INFO: 找到 1 个周菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:252]
2025-06-03 14:37:56,892 INFO:   - 周菜单ID: 37, 开始日期: 2025-06-02, 结束日期: 2025-06-08, 状态: 已发布 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:254]
2025-06-03 14:37:56,896 INFO: 周菜单 37 总共有 77 条食谱记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:260]
2025-06-03 14:37:56,897 INFO:   - 食谱记录: day_of_week=1, meal_type='早餐', recipe_id=371 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:263]
2025-06-03 14:37:56,897 INFO:   - 食谱记录: day_of_week=1, meal_type='早餐', recipe_id=154 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:263]
2025-06-03 14:37:56,897 INFO:   - 食谱记录: day_of_week=1, meal_type='早餐', recipe_id=152 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:263]
2025-06-03 14:37:56,898 INFO:   - 食谱记录: day_of_week=1, meal_type='早餐', recipe_id=368 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:263]
2025-06-03 14:37:56,898 INFO:   - 食谱记录: day_of_week=1, meal_type='早餐', recipe_id=367 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:263]
2025-06-03 14:37:56,898 INFO:   - 食谱记录: day_of_week=1, meal_type='早餐', recipe_id=153 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:263]
2025-06-03 14:37:56,899 INFO:   - 食谱记录: day_of_week=1, meal_type='早餐', recipe_id=369 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:263]
2025-06-03 14:37:56,899 INFO:   - 食谱记录: day_of_week=1, meal_type='午餐', recipe_id=152 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:263]
2025-06-03 14:37:56,899 INFO:   - 食谱记录: day_of_week=1, meal_type='午餐', recipe_id=370 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:263]
2025-06-03 14:37:56,899 INFO:   - 食谱记录: day_of_week=1, meal_type='午餐', recipe_id=369 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:263]
2025-06-03 14:37:56,902 INFO:   - 食谱记录: day_of_week=1, meal_type='午餐', recipe_id=368 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:263]
2025-06-03 14:37:56,902 INFO:   - 食谱记录: day_of_week=1, meal_type='午餐', recipe_id=371 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:263]
2025-06-03 14:37:56,903 INFO:   - 食谱记录: day_of_week=1, meal_type='午餐', recipe_id=367 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:263]
2025-06-03 14:37:56,903 INFO:   - 食谱记录: day_of_week=1, meal_type='午餐', recipe_id=154 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:263]
2025-06-03 14:37:56,904 INFO:   - 食谱记录: day_of_week=1, meal_type='晚餐', recipe_id=154 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:263]
2025-06-03 14:37:56,904 INFO:   - 食谱记录: day_of_week=1, meal_type='晚餐', recipe_id=152 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:263]
2025-06-03 14:37:56,905 INFO:   - 食谱记录: day_of_week=1, meal_type='晚餐', recipe_id=368 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:263]
2025-06-03 14:37:56,905 INFO:   - 食谱记录: day_of_week=1, meal_type='晚餐', recipe_id=369 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:263]
2025-06-03 14:37:56,906 INFO:   - 食谱记录: day_of_week=1, meal_type='晚餐', recipe_id=153 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:263]
2025-06-03 14:37:56,906 INFO:   - 食谱记录: day_of_week=2, meal_type='早餐', recipe_id=371 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:263]
2025-06-03 14:37:56,907 INFO:   - 食谱记录: day_of_week=2, meal_type='早餐', recipe_id=368 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:263]
2025-06-03 14:37:56,907 INFO:   - 食谱记录: day_of_week=2, meal_type='早餐', recipe_id=367 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:263]
