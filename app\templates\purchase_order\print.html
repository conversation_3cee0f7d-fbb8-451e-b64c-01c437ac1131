<!DOCTYPE html>
<html>
<head>
    <title>采购订单打印</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style nonce="{{ csp_nonce }}">
        /* 打印样式 */
        @page {
            size: 210mm 297mm; /* A4纸张精确尺寸 */
            margin: 1.5cm;
        }
        /* 确保打印时背景色显示 */
        * {
            -webkit-print-color-adjust: exact !important;
            color-adjust: exact !important;
        }
        html, body {
            width: 210mm; /* A4宽度 */
            height: 297mm; /* A4高度 */
            margin: 0 auto;
            padding: 0;
            font-family: "Microsoft YaHei", "微软雅黑", SimSun, "宋体", sans-serif;
            font-size: 11pt;
            line-height: 1.6;
            background-color: white;
            color: #333;
        }
        .container {
            width: 180mm; /* A4宽度减去页边距 */
            margin: 0 auto;
            padding: 15px 0;
            box-sizing: border-box;
        }
        .header {
            text-align: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #2c3e50;
        }
        .header h2 {
            margin: 0 0 5px 0;
            font-size: 22pt;
            font-weight: 600;
            color: #2c3e50;
        }
        .header p {
            margin: 5px 0;
            font-size: 12pt;
            color: #555;
        }
        .info-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .info-table th, .info-table td {
            border: 1px solid #ddd;
            padding: 8px 12px;
        }
        .info-table th {
            width: 22%;
            text-align: right;
            background-color: white;
            color: #000;
            font-weight: 600;
            border: 1px solid #000;
        }
        .info-table td {
            background-color: #fff;
        }
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .items-table th, .items-table td {
            border: 1px solid #ddd;
            padding: 10px 8px;
            text-align: center;
            vertical-align: middle;
        }
        .items-table th {
            background-color: white;
            color: #000;
            font-weight: 600;
            font-size: 11pt;
            border: 1px solid #000;
        }
        .items-table tr:nth-child(even) {
            background-color: white;
        }
        .items-table tr:hover {
            background-color: white;
        }
        .footer {
            margin-top: 25px;
            display: flex;
            justify-content: space-between;
        }
        .signature {
            width: 45%;
            padding: 10px;
            border-top: 1px solid #ddd;
        }
        .signature p {
            margin: 5px 0;
        }
        .total-row {
            font-weight: bold;
            background-color: #f5f7fa !important;
            color: #2c3e50;
        }
        .total-row td {
            border-top: 2px solid #ddd !important;
        }
        .price {
            color: #e74c3c;
            font-weight: 600;
        }
        .no-print {
            text-align: center;
            margin-top: 30px;
        }
        .no-print button {
            padding: 8px 20px;
            margin: 0 10px;
            background-color: #3498db;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        .no-print button:hover {
            background-color: #2980b9;
        }
        @media screen {
            body {
                background-color: #f0f0f0;
                padding: 20px 0;
            }
            .container {
                background-color: white;
                box-shadow: 0 0 15px rgba(0,0,0,0.15);
                min-height: 297mm;
                padding: 20px;
                border-radius: 5px;
            }
        }
        @media print {
            html, body {
                width: 210mm;
                height: 297mm;
            }
            .no-print {
                display: none !important;
            }
            .container {
                width: 100%;
                padding: 0;
                box-shadow: none;
            }
            /* 允许表格跨页 */
            table { page-break-inside: auto; }
            tr { page-break-inside: avoid; }
            /* 避免页面元素被分割 */
            .header, .footer { page-break-inside: avoid; }

            /* 表头在每页重复显示 */
            thead { display: table-header-group; }
            tfoot { display: table-footer-group; }

            /* 分页后保持表头样式 */
            .items-table thead tr th {
                background-color: white !important;
                color: #000 !important;
                border: 1px solid #000 !important;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h2>采购订单</h2>
            <p>单号：{{ order.order_number }}</p>
        </div>

        <table class="info-table">
            <tr>
                <th>采购区域</th>
                <td>{{ order.area.name if order.area else current_user.area.name }}</td>
                <th>供应商</th>
                <td>{{ order.supplier_name if order.supplier_name else '自购' }}</td>
            </tr>
            <tr>
                <th>订单日期</th>
                <td>{{ order.order_date|format_datetime('%Y-%m-%d %H:%M') if order.order_date else '-' }}</td>
                <th>预计送达日期</th>
                <td>{{ order.expected_delivery_date|format_datetime('%Y-%m-%d') if order.expected_delivery_date else '-' }}</td>
            </tr>
            <tr>
                <th>订单状态</th>
                <td>{{ order.status_display }}</td>
                <th>创建人</th>
                <td>{{ order.creator_name }}</td>
            </tr>
            <tr>
                <th>审核人</th>
                <td>{{ order.approver_name if order.approver_name else '-' }}</td>
                <th>备注</th>
                <td>
                    {{ order.notes or '-' }}
                    {% if order.status == 'cancelled' %}
                    <br>取消原因：{{ order.cancel_reason or '-' }}
                    {% elif order.status == 'delivered' %}
                    <br>送货备注：{{ order.delivery_notes or '-' }}
                    {% endif %}
                </td>
            </tr>
        </table>

        <table class="items-table">
            <thead>
                <tr>
                    <th style="width: 10%">序号</th>
                    <th class="w-35">食材名称</th>
                    <th class="w-15">采购量</th>
                    <th class="w-15">单位</th>
                    <th class="w-25">供应商</th>
                </tr>
            </thead>
            <tbody>
                {% for item in order.order_items %}
                <tr>
                    <td>{{ loop.index }}</td>
                    <td>{{ item.ingredient_name }}</td>
                    <td>{{ item.quantity|float|round(2) }}</td>
                    <td>{{ item.unit }}</td>
                    <td>{{ item.supplier_name if item.supplier_name else '自购' }}</td>
                </tr>
                {% endfor %}
            </tbody>
            <!-- 空的表脚，用于分页时保持表格结构 -->
            <tfoot>
                <tr style="visibility: hidden; height: 0;">
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                </tr>
            </tfoot>
        </table>

        <div class="footer">
            <div class="signature">
                <p>采购员：{{ order.creator_name }}</p>
                <p>签名：________________</p>
            </div>
            <div class="signature">
                <p>审核人：{{ order.approver_name if order.approver_name else '________________' }}</p>
                <p>签名：________________</p>
            </div>
        </div>

        <div class="footer">
            <div class="signature">
                <p>供应商：{{ order.supplier_name if order.supplier_name else '自购' }}</p>
                <p>签名：________________</p>
            </div>
            <div class="signature">
                <p>打印时间：{{ current_time|format_datetime('%Y-%m-%d %H:%M:%S') }}</p>
            </div>
        </div>

        <div class="no-print" style="text-align: center; margin-top: 20px;">
            <button class="print-button">打印</button>
            <button data-onclick="window.close()">关闭</button>
        </div>

        <script nonce="{{ csp_nonce }}">
            // 页面加载完成后自动聚焦
            window.onload = function() {
                window.focus();

                // 检测打印媒体查询支持
                if (window.matchMedia) {
                    var mediaQueryList = window.matchMedia('print');
                    mediaQueryList.addListener(function(mql) {
                        if (mql.matches) {
                            console.log('打印前准备');
                            document.body.classList.add('printing');

                            // 确保表头在每页都显示
                            var tableHeaders = document.querySelectorAll('thead');
                            tableHeaders.forEach(function(thead) {
                                thead.style.display = 'table-header-group';
                            });

                            // 确保表头样式正确
                            var headerCells = document.querySelectorAll('.items-table thead th');
                            headerCells.forEach(function(th) {
                                th.style.backgroundColor = 'white';
                                th.style.color = '#000';
                                th.style.border = '1px solid #000';
                            });
                        } else {
                            console.log('打印后恢复');
                            document.body.classList.remove('printing');
                        }
                    });
                }

                // 自动调整打印设置
                document.querySelector('.no-print button').addEventListener('click', function() {
                    var style = document.createElement('style');
                    style.innerHTML = '@page { size: A4; margin: 2cm; }';
                    document.head.appendChild(style);

                    // 添加分页支持的样式
                    var pagingStyle = document.createElement('style');
                    pagingStyle.innerHTML = `
                        @media print {
                            thead { display: table-header-group !important; }
                            tfoot { display: table-footer-group !important; }
                            .items-table thead tr th {
                                background-color: white !important;
                                color: #000 !important;
                                border: 1px solid #000 !important;
                            }
                        }
                    `;
                    document.head.appendChild(pagingStyle);

                    setTimeout(function() {
                        window.print();
                    }, 100);
                });
            };
        </script>
    </div>

<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/universal-event-handler.js') }}"></script>
</body>
</html>










