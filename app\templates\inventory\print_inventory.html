<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>库存报表打印</title>
    <style nonce="{{ csp_nonce }}">
        @media print {
            @page {
                size: A4 landscape;
                margin: 0.5cm;
            }
            body {
                margin: 0;
                padding: 0;
                font-family: <PERSON>m<PERSON><PERSON>, "Microsoft YaHei", sans-serif;
                font-size: 12px;
            }
            .no-print {
                display: none !important;
            }
            .page-break {
                page-break-after: always;
            }
        }

        body {
            font-family: SimSun, "Microsoft YaHei", sans-serif;
            font-size: 14px;
            line-height: 1.5;
            color: #000;
            background: #fff;
            margin: 0;
            padding: 20px;
        }

        .print-header {
            text-align: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #000;
        }

        .print-title {
            font-size: 24px;
            font-weight: bold;
            margin: 0 0 10px;
        }

        .print-info {
            margin-bottom: 15px;
            font-size: 12px;
        }

        .info-row {
            display: flex;
            margin-bottom: 5px;
        }

        .info-label {
            width: 100px;
            font-weight: bold;
        }

        .info-value {
            flex: 1;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            font-size: 11px;
        }

        .table th,
        .table td {
            border: 1px solid #000;
            padding: 6px 4px;
            text-align: left;
            vertical-align: middle;
        }

        .table th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-weight: bold;
            text-align: center;
        }

        .table tbody tr:nth-child(even) {
            background-color: #f9f9f9;
        }

        .ingredient-name {
            font-weight: bold;
            color: #2c3e50;
        }

        .ingredient-category {
            font-size: 10px;
            color: #6c757d;
            background-color: #f8f9fa;
            padding: 1px 4px;
            border-radius: 3px;
            display: inline-block;
            margin-top: 2px;
        }

        .quantity-highlight {
            font-weight: bold;
            color: #007bff;
        }

        .status-badge {
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 10px;
            font-weight: bold;
        }

        .status-normal { background-color: #d4edda; color: #155724; }
        .status-warning { background-color: #fff3cd; color: #856404; }
        .status-danger { background-color: #f8d7da; color: #721c24; }
        .status-info { background-color: #d1ecf1; color: #0c5460; }
        .status-secondary { background-color: #e2e3e5; color: #383d41; }

        .print-footer {
            margin-top: 30px;
            padding-top: 15px;
            border-top: 1px solid #000;
            text-align: center;
            font-size: 10px;
        }

        .no-print {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 20px;
            background: #007bff;
            color: #fff;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        .no-print:hover {
            background: #0056b3;
        }

        .summary-stats {
            display: flex;
            justify-content: space-around;
            margin-bottom: 20px;
            padding: 10px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 18px;
            font-weight: bold;
            color: #007bff;
        }

        .stat-label {
            font-size: 12px;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <button class="no-print" id="printBtn">打印库存报表</button>

    <div class="print-header">
        <h1 class="print-title">{{ project_name }} - 库存报表</h1>
    </div>

    <div class="print-info">
        <div class="info-row">
            <span class="info-label">仓库：</span>
            <span class="info-value">{{ filter_info.warehouse }}</span>
            <span class="info-label">食材：</span>
            <span class="info-value">{{ filter_info.ingredient }}</span>
        </div>
        <div class="info-row">
            <span class="info-label">状态：</span>
            <span class="info-value">{{ filter_info.status }}</span>
            <span class="info-label">存储位置：</span>
            <span class="info-value">{{ filter_info.storage_location }}</span>
        </div>
        <div class="info-row">
            <span class="info-label">打印时间：</span>
            <span class="info-value">{{ print_date.strftime('%Y-%m-%d %H:%M:%S') }}</span>
            <span class="info-label">记录数量：</span>
            <span class="info-value">{{ inventories|length }} 条</span>
        </div>
    </div>

    <!-- 统计信息 -->
    <div class="summary-stats">
        <div class="stat-item">
            <div class="stat-number">{{ inventories|length }}</div>
            <div class="stat-label">库存记录</div>
        </div>
        <div class="stat-item">
            <div class="stat-number">{{ inventories|map(attribute='ingredient.name')|unique|list|length }}</div>
            <div class="stat-label">食材种类</div>
        </div>
        <div class="stat-item">
            <div class="stat-number">{{ inventories|map(attribute='warehouse.name')|unique|list|length }}</div>
            <div class="stat-label">仓库数量</div>
        </div>
        <div class="stat-item">
            <div class="stat-number">{{ inventories|selectattr('status', 'equalto', '正常')|list|length }}</div>
            <div class="stat-label">正常库存</div>
        </div>
    </div>

    <table class="table">
        <thead>
            <tr>
                <th style="width: 15%;">食材名称</th>
                <th style="width: 10%;">仓库</th>
                <th style="width: 12%;">存储位置</th>
                <th style="width: 12%;">批次号</th>
                <th style="width: 8%;">数量</th>
                <th style="width: 6%;">单位</th>
                <th style="width: 10%;">生产日期</th>
                <th style="width: 10%;">过期日期</th>
                <th style="width: 8%;">状态</th>
                <th style="width: 9%;">备注</th>
            </tr>
        </thead>
        <tbody>
            {% for inventory in inventories %}
            <tr>
                <td>
                    <div class="ingredient-name">{{ inventory.ingredient.name }}</div>
                    {% if inventory.ingredient.category %}
                    <div class="ingredient-category">{{ inventory.ingredient.category.name }}</div>
                    {% endif %}
                </td>
                <td>{{ inventory.warehouse.name }}</td>
                <td>
                    {{ inventory.storage_location.name }}
                    <br><small>({{ inventory.storage_location.location_code }})</small>
                </td>
                <td>{{ inventory.batch_number }}</td>
                <td class="quantity-highlight">{{ inventory.quantity }}</td>
                <td>{{ inventory.unit }}</td>
                <td>{{ inventory.production_date.strftime('%Y-%m-%d') if inventory.production_date else '-' }}</td>
                <td>{{ inventory.expiry_date.strftime('%Y-%m-%d') if inventory.expiry_date else '-' }}</td>
                <td>
                    {% if inventory.status == '正常' %}
                    <span class="status-badge status-normal">正常</span>
                    {% elif inventory.status == '待检' %}
                    <span class="status-badge status-warning">待检</span>
                    {% elif inventory.status == '冻结' %}
                    <span class="status-badge status-info">冻结</span>
                    {% elif inventory.status == '已过期' %}
                    <span class="status-badge status-danger">已过期</span>
                    {% elif inventory.status == '已用完' %}
                    <span class="status-badge status-secondary">已用完</span>
                    {% endif %}
                </td>
                <td>{{ inventory.notes or '-' }}</td>
            </tr>
            {% else %}
            <tr>
                <td colspan="10" style="text-align: center; padding: 20px;">
                    暂无库存数据
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>

    <div class="print-footer">
        <p>本报表由校园餐智慧食堂平台生成，确保库存信息准确可靠</p>
        <p>打印时间：{{ print_date.strftime('%Y年%m月%d日 %H:%M:%S') }}</p>
    </div>

    <script nonce="{{ csp_nonce }}">
        window.onload = function() {
            // 自动聚焦
            window.focus();

            // 绑定打印按钮事件
            const printBtn = document.getElementById('printBtn');
            if (printBtn) {
                printBtn.addEventListener('click', function() {
                    window.print();
                });
            }
        };
    </script>
</body>
</html>
