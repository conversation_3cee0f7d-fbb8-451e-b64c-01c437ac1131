<!DOCTYPE html>
<html>
<head>
    <title>消耗计划详情</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <style nonce="{{ csp_nonce }}">
        /* 打印样式 */
        @page {
            size: 210mm 297mm; /* A4纸张精确尺寸 */
            margin: 1.5cm;
        }
        /* 确保打印时背景色显示 */
        * {
            -webkit-print-color-adjust: exact !important;
            color-adjust: exact !important;
        }
        html, body {
            width: 210mm; /* A4宽度 */
            height: 297mm; /* A4高度 */
            margin: 0 auto;
            padding: 0;
            font-family: "Microsoft YaHei", "微软雅黑", SimSun, "宋体", sans-serif;
            font-size: 11pt;
            line-height: 1.6;
            background-color: white;
            color: #333;
        }

        .container {
            width: 180mm; /* A4宽度减去页边距 */
            margin: 0 auto;
            padding: 15px 0;
            box-sizing: border-box;
        }

        /* 页眉 */
        .header {
            text-align: center;
            margin-bottom: 20px;
            page-break-inside: avoid;
        }

        .header h2 {
            font-size: 18pt;
            font-weight: bold;
            margin-bottom: 8px;
            color: #2c3e50;
        }

        .header p {
            font-size: 12pt;
            color: #666;
            margin: 4px 0;
        }

        /* 信息表格 */
        .info-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            border: 2px solid #2c3e50;
        }

        .info-table th,
        .info-table td {
            border: 1px solid #2c3e50;
            padding: 8px;
            vertical-align: top;
            font-size: 10pt;
        }

        .info-table th {
            background-color: white;
            color: #000;
            font-weight: bold;
            text-align: center;
            width: 15%;
        }

        /* 明细表格 */
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            border: 2px solid #2c3e50;
        }

        .items-table th,
        .items-table td {
            border: 1px solid #2c3e50;
            padding: 6px;
            text-align: center;
            vertical-align: middle;
            font-size: 10pt;
        }

        .items-table th {
            background-color: white;
            color: #000;
            font-weight: bold;
        }

        .items-table td {
            background-color: white;
        }

        /* 状态标签 */
        .status-badge {
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 9pt;
            font-weight: bold;
        }

        .status-planning { background-color: #ffc107; color: #000; }
        .status-approved { background-color: #17a2b8; color: white; }
        .status-executed { background-color: #28a745; color: white; }
        .status-cancelled { background-color: #dc3545; color: white; }

        .stock-sufficient { background-color: #28a745; color: white; }
        .stock-insufficient { background-color: #dc3545; color: white; }

        /* 签名区域 */
        .footer {
            display: flex;
            justify-content: space-between;
            margin-top: 30px;
            page-break-inside: avoid;
        }

        .signature {
            width: 45%;
            text-align: left;
        }

        .signature p {
            margin: 8px 0;
            font-size: 10pt;
            line-height: 1.8;
        }

        /* 屏幕预览样式 */
        @media screen {
            body {
                background: #f8f9fa;
                padding: 20px;
                font-size: 14px;
            }

            .container {
                background: white;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                border-radius: 8px;
                padding: 30px;
                margin: 0 auto;
                max-width: 1000px;
            }

            .action-buttons {
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 1000;
                display: flex;
                gap: 8px;
            }

            .action-buttons .btn {
                padding: 10px 16px;
                border: none;
                border-radius: 6px;
                cursor: pointer;
                text-decoration: none;
                display: inline-flex;
                align-items: center;
                gap: 6px;
                font-size: 13px;
                font-weight: 500;
                transition: all 0.2s ease;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }

            .btn-primary { background: #007bff; color: white; }
            .btn-info { background: #17a2b8; color: white; }
            .btn-success { background: #28a745; color: white; }
            .btn-danger { background: #dc3545; color: white; }
            .btn-secondary { background: #6c757d; color: white; }

            .btn:hover {
                opacity: 0.9;
                transform: translateY(-1px);
                box-shadow: 0 4px 8px rgba(0,0,0,0.15);
            }

            .header {
                border-bottom: 2px solid #e9ecef;
                padding-bottom: 20px;
                margin-bottom: 30px;
            }

            .title {
                font-size: 28px;
                color: #2c3e50;
                margin-bottom: 8px;
            }

            .subtitle {
                font-size: 16px;
                color: #6c757d;
            }

            .info-table th {
                background-color: #f8f9fa;
                color: #495057;
                font-size: 13px;
            }

            .info-table td {
                font-size: 14px;
            }

            .detail-table th {
                background-color: #e9ecef;
                color: #495057;
                font-size: 13px;
            }

            .detail-table td {
                font-size: 13px;
            }
        }

        @media print {
            html, body {
                width: 210mm;
                height: 297mm;
            }
            .no-print {
                display: none !important;
            }
            .container {
                width: 100%;
                padding: 0;
                box-shadow: none;
            }
            /* 允许表格跨页 */
            table { page-break-inside: auto; }
            tr { page-break-inside: avoid; }
            /* 避免页面元素被分割 */
            .header, .footer { page-break-inside: avoid; }

            /* 表头在每页重复显示 */
            thead { display: table-header-group; }
            tfoot { display: table-footer-group; }

            /* 分页后保持表头样式 */
            .items-table thead tr th {
                background-color: white !important;
                color: #000 !important;
                border: 1px solid #2c3e50 !important;
            }
            .info-table thead tr th {
                background-color: white !important;
                color: #000 !important;
                border: 1px solid #2c3e50 !important;
            }
        }
    </style>
</head>
<body>
    <!-- 操作按钮（仅在屏幕上显示） -->
    <div class="action-buttons no-print">
        {% if consumption_plan.status == '计划中' %}
            <button class="btn btn-info" data-onclick="approveConfirm('{{ url_for('consumption_plan.approve', id=consumption_plan.id) }}')">
                <i class="fas fa-check"></i> 审核
            </button>
            <button class="btn btn-danger" data-onclick="cancelConfirm('{{ url_for('consumption_plan.cancel', id=consumption_plan.id) }}')">
                <i class="fas fa-times"></i> 取消
            </button>
        {% elif consumption_plan.status == '已审核' %}
            <button class="btn btn-success" data-onclick="executeConfirm('{{ url_for('consumption_plan.execute', id=consumption_plan.id) }}')">
                <i class="fas fa-play"></i> 执行
            </button>
        {% endif %}
        <a href="{{ url_for('consumption_plan.index') }}" class="btn btn-secondary">
            <i class="fas fa-list"></i> 返回列表
        </a>
    </div>

    <div class="container">
        <div class="header">
            <h2>消耗计划</h2>
            <p>单号：{{ consumption_plan.id }}</p>
        </div>

        <!-- 基本信息表格 -->
        <table class="info-table">
            <tr>
                <th>消耗日期</th>
                <td>
                    {% if consumption_plan.consumption_date %}
                        {{ consumption_plan.consumption_date.strftime('%Y年%m月%d日') }}
                    {% elif menu_plan %}
                        {{ menu_plan.plan_date.strftime('%Y年%m月%d日') }}
                    {% else %}
                        -
                    {% endif %}
                </td>
                <th>餐次</th>
                <td>
                    {% if consumption_plan.meal_type %}
                        {{ consumption_plan.meal_type }}
                    {% elif menu_plan %}
                        {{ menu_plan.meal_type }}
                    {% else %}
                        -
                    {% endif %}
                </td>
                <th>用餐人数</th>
                <td>
                    {% if consumption_plan.diners_count %}
                        {{ consumption_plan.diners_count }}人
                    {% elif menu_plan and menu_plan.expected_diners %}
                        {{ menu_plan.expected_diners }}人
                    {% else %}
                        -
                    {% endif %}
                </td>
            </tr>
            <tr>
                <th>学校</th>
                <td>
                    {% if menu_plan %}
                        {{ menu_plan.area.name }}
                    {% else %}
                        {% if area_name %}
                            {{ area_name }}
                        {% else %}
                            -
                        {% endif %}
                    {% endif %}
                </td>
                <th>状态</th>
                <td>
                    {% if consumption_plan.status == '计划中' %}
                        <span class="status-badge status-planning">计划中</span>
                    {% elif consumption_plan.status == '已审核' %}
                        <span class="status-badge status-approved">已审核</span>
                    {% elif consumption_plan.status == '已执行' %}
                        <span class="status-badge status-executed">已执行</span>
                    {% elif consumption_plan.status == '已取消' %}
                        <span class="status-badge status-cancelled">已取消</span>
                    {% endif %}
                </td>
                <th>创建人</th>
                <td>{{ consumption_plan.creator.real_name or consumption_plan.creator.username }}</td>
            </tr>
            <tr>
                <th>创建时间</th>
                <td>{{ consumption_plan.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                <th>审批人</th>
                <td>{{ consumption_plan.approver.real_name or consumption_plan.approver.username if consumption_plan.approver else '-' }}</td>
                <th>备注</th>
                <td>{{ consumption_plan.notes or '-' }}</td>
            </tr>
        </table>

        <!-- 消耗明细表格 -->
        <table class="items-table">
            <thead>
                <tr>
                    <th style="width: 25%">食材名称</th>
                    <th style="width: 12%">计划消耗量</th>
                    <th style="width: 12%">实际消耗量</th>
                    <th style="width: 8%">单位</th>
                    <th style="width: 10%">当前库存</th>
                    <th style="width: 10%">库存状态</th>
                    <th style="width: 10%">出库状态</th>
                    <th style="width: 13%">备注</th>
                </tr>
            </thead>
            <tbody>
                {% for detail in consumption_details %}
                <tr>
                    <td style="text-align: left; padding-left: 8px;">{{ detail.ingredient.name }}</td>
                    <td>{{ detail.planned_quantity }}</td>
                    <td>{{ detail.actual_quantity or '-' }}</td>
                    <td>{{ detail.unit }}</td>
                    <td>{{ inventory_status[detail.id].total if inventory_status and detail.id in inventory_status else '-' }}</td>
                    <td>
                        {% if inventory_status and detail.id in inventory_status %}
                            {% if inventory_status[detail.id].sufficient %}
                                <span class="status-badge stock-sufficient">库存充足</span>
                            {% else %}
                                <span class="status-badge stock-insufficient">库存不足</span>
                            {% endif %}
                        {% else %}
                            -
                        {% endif %}
                    </td>
                    <td>
                        {% if detail.status == '待出库' %}
                            <span class="status-badge status-planning">待出库</span>
                        {% elif detail.status == '已出库' %}
                            <span class="status-badge status-executed">已出库</span>
                        {% else %}
                            -
                        {% endif %}
                    </td>
                    <td>{{ detail.notes or '-' }}</td>
                </tr>
                {% else %}
                <tr>
                    <td colspan="8" style="text-align: center; color: #999;">暂无消耗明细</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>

        <!-- 出库单信息（如果已执行） -->
        {% if consumption_plan.status == '已执行' and stock_out %}
        <table class="info-table">
            <tr>
                <th>出库单号</th>
                <td>{{ stock_out.stock_out_number }}</td>
                <th>出库日期</th>
                <td>{{ stock_out.stock_out_date.strftime('%Y-%m-%d %H:%M') if stock_out.stock_out_date else '-' }}</td>
                <th>操作人</th>
                <td>{{ stock_out.operator.real_name or stock_out.operator.username if stock_out.operator else '-' }}</td>
            </tr>
        </table>
        {% endif %}

        <div class="footer">
            <div class="signature">
                <p>制表人：{{ consumption_plan.creator.real_name or consumption_plan.creator.username }}</p>
                <p>签名：________________</p>
            </div>
            <div class="signature">
                <p>审核人：{{ consumption_plan.approver.real_name or consumption_plan.approver.username if consumption_plan.approver else '________________' }}</p>
                <p>签名：________________</p>
            </div>
        </div>

        <div class="footer">
            <div class="signature">
                <p>执行人：{{ stock_out.operator.real_name or stock_out.operator.username if stock_out and stock_out.operator else '________________' }}</p>
                <p>签名：________________</p>
            </div>
            <div class="signature">
                <p>打印时间：{{ current_time.strftime('%Y-%m-%d %H:%M:%S') if current_time else '' }}</p>
            </div>
        </div>

        <div class="no-print" style="text-align: center; margin-top: 20px;">
            <button class="print-button">打印</button>
            <button data-onclick="window.close()">关闭</button>
        </div>
    </div>

<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/comprehensive-event-handler.js') }}"></script>
</body>
</html>
