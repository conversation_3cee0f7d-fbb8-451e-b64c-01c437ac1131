{% extends "base.html" %}

{% block title %}库存统计分析{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2><i class="fas fa-chart-bar text-primary"></i> 库存统计分析</h2>
            {% if warehouse %}
            <p class="text-muted mb-0">
                <i class="fas fa-warehouse"></i> 当前仓库：{{ warehouse.name }}
                {% if storage_locations %}
                <span class="ms-3"><i class="fas fa-map-marker-alt"></i> 储存位置：{{ storage_locations|length }} 个</span>
                {% endif %}
            </p>
            {% endif %}
        </div>
        <div class="btn-group">
            <a href="{{ url_for('inventory.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> 返回库存
            </a>
        </div>
    </div>

    <!-- 筛选条件 -->
    <div class="card mb-4">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="fas fa-filter text-primary"></i> 筛选条件</h5>
        </div>
        <div class="card-body">
            <form id="filterForm">
                <!-- 第一行：基础筛选 -->
                <div class="row g-3 mb-3">
                    <div class="col-lg-3 col-md-6">
                        <label for="stat_type" class="form-label fw-bold">
                            <i class="fas fa-chart-line text-info"></i> 统计类型
                        </label>
                        <select class="form-select" id="stat_type" name="type">
                            <option value="time">📅 按时间统计</option>
                            <option value="ingredient">🥬 按食材统计</option>
                            <option value="supplier">🏪 按供应商统计</option>
                        </select>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <label for="start_date" class="form-label fw-bold">
                            <i class="fas fa-calendar-alt text-success"></i> 开始日期
                        </label>
                        <input type="date" class="form-control" id="start_date" name="start_date">
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <label for="end_date" class="form-label fw-bold">
                            <i class="fas fa-calendar-check text-success"></i> 结束日期
                        </label>
                        <input type="date" class="form-control" id="end_date" name="end_date">
                    </div>
                    <div class="col-lg-3 col-md-6 d-flex align-items-end">
                        <button type="button" class="btn btn-primary btn-lg w-100" onclick="loadStatistics()">
                            <i class="fas fa-search"></i> 开始查询
                        </button>
                    </div>
                </div>

                <!-- 第二行：高级筛选 -->
                <div class="row g-3">
                    <div class="col-lg-3 col-md-6">
                        <label for="storage_location_id" class="form-label fw-bold">
                            <i class="fas fa-map-marker-alt text-warning"></i> 储存位置
                        </label>
                        <select class="form-select" id="storage_location_id" name="storage_location_id">
                            <option value="">📦 全部位置</option>
                            {% for location in storage_locations %}
                            <option value="{{ location.id }}">{{ location.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <label for="category_id" class="form-label fw-bold">
                            <i class="fas fa-tags text-info"></i> 食材分类
                        </label>
                        <select class="form-select" id="category_id" name="category_id">
                            <option value="">🏷️ 全部分类</option>
                            {% for category in categories %}
                            <option value="{{ category.id }}">{{ category.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-lg-3 col-md-6" id="supplierCol">
                        <label for="supplier_id" class="form-label fw-bold">
                            <i class="fas fa-truck text-primary"></i> 供应商
                        </label>
                        <select class="form-select" id="supplier_id" name="supplier_id">
                            <option value="">🏪 全部供应商</option>
                            {% for supplier in suppliers %}
                            <option value="{{ supplier.id }}">{{ supplier.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-lg-3 col-md-6 d-flex align-items-end">
                        <button type="button" class="btn btn-outline-secondary w-100" onclick="resetFilters()">
                            <i class="fas fa-undo"></i> 重置条件
                        </button>
                    </div>
                </div>

                <!-- 快捷时间选择 -->
                <div class="row mt-3">
                    <div class="col-12">
                        <div class="d-flex flex-wrap gap-2">
                            <small class="text-muted align-self-center me-2">快捷选择：</small>
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="setDateRange(7)">最近7天</button>
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="setDateRange(30)">最近30天</button>
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="setDateRange(90)">最近3个月</button>
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="setCurrentMonth()">本月</button>
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="setLastMonth()">上月</button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- 统计结果 -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0"><i class="fas fa-table"></i> 统计结果</h5>
            <div class="btn-group">
                <button type="button" class="btn btn-outline-success" onclick="exportData()">
                    <i class="fas fa-file-excel"></i> 导出Excel
                </button>
                <button type="button" class="btn btn-outline-primary" onclick="printStatistics()">
                    <i class="fas fa-print"></i> 打印报表
                </button>
            </div>
        </div>
        <div class="card-body">
            <!-- 加载提示 -->
            <div id="loadingDiv" class="text-center py-5" style="display: none;">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <p class="mt-2">正在加载统计数据...</p>
            </div>

            <!-- 统计图表 -->
            <div id="chartContainer" style="display: none;">
                <canvas id="statisticsChart" width="400" height="200"></canvas>
            </div>

            <!-- 统计表格 -->
            <div id="tableContainer" style="display: none;">
                <div class="table-responsive">
                    <table class="table table-striped table-hover" id="statisticsTable">
                        <thead class="table-dark">
                            <!-- 表头将通过JavaScript动态生成 -->
                        </thead>
                        <tbody>
                            <!-- 数据将通过JavaScript动态生成 -->
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 空数据提示 -->
            <div id="noDataDiv" class="text-center py-5" style="display: none;">
                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                <p class="text-muted">暂无统计数据</p>
                <p class="text-muted">请调整筛选条件后重新查询</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script nonce="{{ csp_nonce }}">
    let currentChart = null;
    let currentData = null;

    // 页面加载时设置默认日期
    document.addEventListener('DOMContentLoaded', function() {
        // 设置默认日期为最近30天
        const endDate = new Date();
        const startDate = new Date();
        startDate.setDate(startDate.getDate() - 30);
        
        document.getElementById('start_date').value = startDate.toISOString().split('T')[0];
        document.getElementById('end_date').value = endDate.toISOString().split('T')[0];
        
        // 监听统计类型变化（供应商筛选始终显示）
        document.getElementById('stat_type').addEventListener('change', function() {
            // 供应商筛选现在始终显示，不需要隐藏
        });
        
        // 自动加载默认统计
        loadStatistics();
    });

    // 加载统计数据
    function loadStatistics() {
        const formData = new FormData(document.getElementById('filterForm'));
        const params = new URLSearchParams(formData);
        
        // 显示加载状态
        showLoading();
        
        fetch(`{{ url_for('inventory.statistics_data') }}?${params}`)
            .then(response => response.json())
            .then(result => {
                hideLoading();
                if (result.success) {
                    currentData = result.data;
                    displayStatistics(result.data, formData.get('type'));
                } else {
                    showError(result.message);
                }
            })
            .catch(error => {
                hideLoading();
                showError('加载统计数据失败: ' + error.message);
            });
    }

    // 显示统计结果
    function displayStatistics(data, type) {
        if (!data || (Array.isArray(data) && data.length === 0) || 
            (data.stock_in && data.stock_in.length === 0 && data.stock_out && data.stock_out.length === 0)) {
            showNoData();
            return;
        }

        if (type === 'time') {
            displayTimeStatistics(data);
        } else if (type === 'ingredient') {
            displayIngredientStatistics(data);
        } else if (type === 'supplier') {
            displaySupplierStatistics(data);
        }
        
        showTable();
    }

    // 显示按时间统计
    function displayTimeStatistics(data) {
        const table = document.getElementById('statisticsTable');
        
        // 设置表头
        table.querySelector('thead').innerHTML = `
            <tr>
                <th>日期</th>
                <th>入库单数</th>
                <th>入库项目</th>
                <th>入库金额</th>
                <th>出库单数</th>
                <th>出库项目</th>
                <th>出库金额</th>
                <th>净流量</th>
            </tr>
        `;
        
        // 合并入库出库数据
        const dateMap = new Map();
        
        // 处理入库数据
        data.stock_in.forEach(item => {
            dateMap.set(item.date, {
                date: item.date,
                stock_in_count: item.stock_in_count || 0,
                stock_in_items: item.stock_in_items || 0,
                stock_in_amount: item.stock_in_amount || 0,
                stock_out_count: 0,
                stock_out_items: 0,
                stock_out_amount: 0
            });
        });
        
        // 处理出库数据
        data.stock_out.forEach(item => {
            if (dateMap.has(item.date)) {
                const existing = dateMap.get(item.date);
                existing.stock_out_count = item.stock_out_count || 0;
                existing.stock_out_items = item.stock_out_items || 0;
                existing.stock_out_amount = item.stock_out_amount || 0;
            } else {
                dateMap.set(item.date, {
                    date: item.date,
                    stock_in_count: 0,
                    stock_in_items: 0,
                    stock_in_amount: 0,
                    stock_out_count: item.stock_out_count || 0,
                    stock_out_items: item.stock_out_items || 0,
                    stock_out_amount: item.stock_out_amount || 0
                });
            }
        });
        
        // 生成表格内容
        const tbody = table.querySelector('tbody');
        tbody.innerHTML = '';
        
        Array.from(dateMap.values()).sort((a, b) => new Date(a.date) - new Date(b.date)).forEach(item => {
            const netAmount = item.stock_in_amount - item.stock_out_amount;
            const row = `
                <tr>
                    <td>${item.date}</td>
                    <td>${item.stock_in_count}</td>
                    <td>${item.stock_in_items}</td>
                    <td class="text-success">¥${item.stock_in_amount.toFixed(2)}</td>
                    <td>${item.stock_out_count}</td>
                    <td>${item.stock_out_items}</td>
                    <td class="text-danger">¥${item.stock_out_amount.toFixed(2)}</td>
                    <td class="${netAmount >= 0 ? 'text-success' : 'text-danger'}">¥${netAmount.toFixed(2)}</td>
                </tr>
            `;
            tbody.innerHTML += row;
        });
    }

    // 显示按食材统计
    function displayIngredientStatistics(data) {
        const table = document.getElementById('statisticsTable');
        
        // 设置表头
        table.querySelector('thead').innerHTML = `
            <tr>
                <th>食材名称</th>
                <th>分类</th>
                <th>入库次数</th>
                <th>入库总量</th>
                <th>入库金额</th>
                <th>出库总量</th>
                <th>出库金额</th>
                <th>库存余量</th>
                <th>周转率</th>
                <th>平均单价</th>
            </tr>
        `;
        
        // 生成表格内容
        const tbody = table.querySelector('tbody');
        tbody.innerHTML = '';
        
        data.forEach(item => {
            const row = `
                <tr>
                    <td><strong>${item.ingredient_name}</strong></td>
                    <td><span class="badge bg-secondary">${item.category_name}</span></td>
                    <td>${item.stock_in_count}</td>
                    <td>${item.total_in_quantity.toFixed(2)}</td>
                    <td class="text-success">¥${item.total_in_amount.toFixed(2)}</td>
                    <td>${item.total_out_quantity.toFixed(2)}</td>
                    <td class="text-danger">¥${item.total_out_amount.toFixed(2)}</td>
                    <td class="${item.net_quantity >= 0 ? 'text-success' : 'text-warning'}">${item.net_quantity.toFixed(2)}</td>
                    <td><span class="badge ${item.turnover_rate >= 80 ? 'bg-success' : item.turnover_rate >= 50 ? 'bg-warning' : 'bg-danger'}">${item.turnover_rate}%</span></td>
                    <td>¥${item.avg_unit_price.toFixed(2)}</td>
                </tr>
            `;
            tbody.innerHTML += row;
        });
    }

    // 显示按供应商统计
    function displaySupplierStatistics(data) {
        const table = document.getElementById('statisticsTable');
        
        // 设置表头
        table.querySelector('thead').innerHTML = `
            <tr>
                <th>供应商名称</th>
                <th>联系人</th>
                <th>电话</th>
                <th>供应次数</th>
                <th>食材种类</th>
                <th>供应总量</th>
                <th>供应金额</th>
                <th>供应频率</th>
                <th>首次供应</th>
                <th>最近供应</th>
            </tr>
        `;
        
        // 生成表格内容
        const tbody = table.querySelector('tbody');
        tbody.innerHTML = '';
        
        data.forEach(item => {
            const row = `
                <tr>
                    <td><strong>${item.supplier_name}</strong></td>
                    <td>${item.contact_person}</td>
                    <td>${item.phone}</td>
                    <td>${item.stock_in_count}</td>
                    <td><span class="badge bg-info">${item.ingredient_types}</span></td>
                    <td>${item.total_quantity.toFixed(2)}</td>
                    <td class="text-success">¥${item.total_amount.toFixed(2)}</td>
                    <td><span class="badge ${item.supply_frequency >= 50 ? 'bg-success' : item.supply_frequency >= 20 ? 'bg-warning' : 'bg-secondary'}">${item.supply_frequency}%</span></td>
                    <td>${item.first_supply_date}</td>
                    <td>${item.last_supply_date}</td>
                </tr>
            `;
            tbody.innerHTML += row;
        });
    }

    // 显示加载状态
    function showLoading() {
        document.getElementById('loadingDiv').style.display = 'block';
        document.getElementById('tableContainer').style.display = 'none';
        document.getElementById('chartContainer').style.display = 'none';
        document.getElementById('noDataDiv').style.display = 'none';
    }

    // 隐藏加载状态
    function hideLoading() {
        document.getElementById('loadingDiv').style.display = 'none';
    }

    // 显示表格
    function showTable() {
        document.getElementById('tableContainer').style.display = 'block';
        document.getElementById('noDataDiv').style.display = 'none';
    }

    // 显示无数据
    function showNoData() {
        document.getElementById('noDataDiv').style.display = 'block';
        document.getElementById('tableContainer').style.display = 'none';
        document.getElementById('chartContainer').style.display = 'none';
    }

    // 显示错误
    function showError(message) {
        alert('错误: ' + message);
    }

    // 打印统计报表
    function printStatistics() {
        const formData = new FormData(document.getElementById('filterForm'));
        const params = new URLSearchParams(formData);
        const printUrl = "{{ url_for('inventory.print_statistics') }}" + "?" + params.toString();
        window.open(printUrl, '_blank');
    }

    // 导出Excel（待实现）
    function exportData() {
        alert('Excel导出功能开发中...');
    }

    // 重置筛选条件
    function resetFilters() {
        document.getElementById('stat_type').value = 'time';
        document.getElementById('storage_location_id').value = '';
        document.getElementById('category_id').value = '';
        document.getElementById('supplier_id').value = '';

        // 重置为最近30天
        const endDate = new Date();
        const startDate = new Date();
        startDate.setDate(startDate.getDate() - 30);

        document.getElementById('start_date').value = startDate.toISOString().split('T')[0];
        document.getElementById('end_date').value = endDate.toISOString().split('T')[0];

        // 重新加载统计
        loadStatistics();
    }

    // 设置日期范围
    function setDateRange(days) {
        const endDate = new Date();
        const startDate = new Date();
        startDate.setDate(startDate.getDate() - days);

        document.getElementById('start_date').value = startDate.toISOString().split('T')[0];
        document.getElementById('end_date').value = endDate.toISOString().split('T')[0];

        // 自动查询
        loadStatistics();
    }

    // 设置本月
    function setCurrentMonth() {
        const now = new Date();
        const startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        const endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0);

        document.getElementById('start_date').value = startDate.toISOString().split('T')[0];
        document.getElementById('end_date').value = endDate.toISOString().split('T')[0];

        // 自动查询
        loadStatistics();
    }

    // 设置上月
    function setLastMonth() {
        const now = new Date();
        const startDate = new Date(now.getFullYear(), now.getMonth() - 1, 1);
        const endDate = new Date(now.getFullYear(), now.getMonth(), 0);

        document.getElementById('start_date').value = startDate.toISOString().split('T')[0];
        document.getElementById('end_date').value = endDate.toISOString().split('T')[0];

        // 自动查询
        loadStatistics();
    }
</script>
{% endblock %}
