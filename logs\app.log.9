2025-06-03 14:37:56,942 INFO:   - 食谱记录: day_of_week=7, meal_type='早餐', recipe_id=368 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:263]
2025-06-03 14:37:56,954 INFO:   - 食谱记录: day_of_week=7, meal_type='午餐', recipe_id=368 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:263]
2025-06-03 14:37:56,955 INFO:   - 食谱记录: day_of_week=7, meal_type='晚餐', recipe_id=368 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:263]
2025-06-03 14:37:56,960 INFO: 匹配条件 day_of_week=2, meal_type='早餐+午餐+晚餐' 的食谱有 0 个 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:272]
2025-06-03 14:37:56,961 INFO: 从周菜单读取到 0 个食谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:275]
2025-06-03 14:37:56,961 INFO: 最终获取到 0 个食谱用于关联显示 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:280]
2025-06-03 14:37:56,967 INFO: 步骤1: 读取消耗日期: 2025-06-03, 餐次: 早餐+午餐+晚餐 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:27]
2025-06-03 14:37:56,968 INFO: 步骤1完成: 找到 0 个菜谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:73]
2025-06-03 14:37:56,976 INFO: 步骤2: 为出库食材 '鸡肉' 查找对应菜谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:80]
2025-06-03 14:37:56,980 INFO: 步骤2: 为出库食材 '羊肉' 查找对应菜谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:80]
2025-06-03 14:37:56,982 INFO: 步骤2: 为出库食材 '胡萝卜' 查找对应菜谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:80]
2025-06-03 14:37:56,987 INFO: 步骤2: 为出库食材 '莲藕' 查找对应菜谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:80]
2025-06-03 14:37:56,989 INFO: 步骤2: 为出库食材 '韭菜' 查找对应菜谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:80]
2025-06-03 14:37:56,990 INFO: 步骤2: 为出库食材 '木耳' 查找对应菜谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:80]
2025-06-03 14:37:56,992 INFO: 步骤2: 为出库食材 '蒜' 查找对应菜谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:80]
2025-06-03 14:37:57,048 INFO: 步骤2: 为出库食材 '吐司' 查找对应菜谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:80]
2025-06-03 14:37:57,053 INFO: 步骤2: 为出库食材 '芹菜' 查找对应菜谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:80]
2025-06-03 14:37:57,058 INFO: 步骤2: 为出库食材 '南瓜' 查找对应菜谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:80]
2025-06-03 14:37:57,061 INFO: 步骤2: 为出库食材 '黄瓜' 查找对应菜谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:80]
2025-06-03 14:37:57,062 INFO: 步骤2: 为出库食材 '米饭' 查找对应菜谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:80]
2025-06-03 14:37:57,063 INFO: 步骤2: 为出库食材 '米饭' 查找对应菜谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:80]
2025-06-03 14:37:57,063 INFO: 溯源完成: 找到 0 条溯源记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:137]
2025-06-03 14:37:57,071 ERROR: 获取溯源信息失败: 'StockIn' object has no attribute 'supplier' [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:396]
2025-06-03 14:37:57,075 ERROR: 获取溯源信息失败: 'StockIn' object has no attribute 'supplier' [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:396]
2025-06-03 14:37:57,077 ERROR: 获取溯源信息失败: 'StockIn' object has no attribute 'supplier' [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:396]
2025-06-03 14:37:57,081 ERROR: 获取溯源信息失败: 'StockIn' object has no attribute 'supplier' [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:396]
2025-06-03 14:37:57,082 ERROR: 获取溯源信息失败: 'StockIn' object has no attribute 'supplier' [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:396]
2025-06-03 14:37:57,086 ERROR: 获取溯源信息失败: 'StockIn' object has no attribute 'supplier' [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:396]
2025-06-03 14:37:57,103 ERROR: 获取溯源信息失败: 'StockIn' object has no attribute 'supplier' [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:396]
2025-06-03 14:37:57,105 ERROR: 获取溯源信息失败: 'StockIn' object has no attribute 'supplier' [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:396]
2025-06-03 14:37:57,107 ERROR: 获取溯源信息失败: 'StockIn' object has no attribute 'supplier' [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:396]
2025-06-03 14:37:57,108 ERROR: 获取溯源信息失败: 'StockIn' object has no attribute 'supplier' [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:396]
2025-06-03 14:37:57,109 ERROR: 获取溯源信息失败: 'StockIn' object has no attribute 'supplier' [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:396]
2025-06-03 14:37:57,110 ERROR: 获取溯源信息失败: 'StockIn' object has no attribute 'supplier' [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:396]
2025-06-03 14:37:57,116 ERROR: 获取溯源信息失败: 'StockIn' object has no attribute 'supplier' [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:396]
2025-06-03 14:38:07,327 INFO: 开始溯源出库单: CK20250603133257 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\food_trace.py:129]
2025-06-03 14:38:07,329 INFO: 查询菜谱：日期=2025-06-03, 星期=1(0=周一), day_of_week=2, 餐次=早餐+午餐+晚餐, 区域ID=42 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\food_trace.py:322]
2025-06-03 14:38:07,333 INFO: 找到 1 个周菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\food_trace.py:332]
2025-06-03 14:38:07,338 INFO: 匹配条件的食谱有 0 个 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\food_trace.py:344]
2025-06-03 14:38:07,385 ERROR: 获取供应商信息失败: 'StockIn' object has no attribute 'supplier' [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\food_trace.py:480]
2025-06-03 14:38:07,389 ERROR: 获取供应商信息失败: 'StockIn' object has no attribute 'supplier' [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\food_trace.py:480]
2025-06-03 14:38:07,397 ERROR: 获取供应商信息失败: 'StockIn' object has no attribute 'supplier' [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\food_trace.py:480]
2025-06-03 14:38:07,397 INFO: 食材一致性分析完成: 匹配率=0%, 缺失=0, 多余=12 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\food_trace.py:551]
2025-06-03 14:38:09,199 INFO: 查询菜谱：日期=2025-06-03, 星期=1(0=周一), day_of_week=2, 餐次=早餐, 区域ID=42 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\food_trace.py:322]
2025-06-03 14:38:09,204 INFO: 找到 1 个周菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\food_trace.py:332]
2025-06-03 14:38:09,208 INFO: 匹配条件的食谱有 6 个 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\food_trace.py:344]
2025-06-03 14:38:09,226 INFO:   - 食谱: 鲜椒青瓜干（朝阳区实验中学版） [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\food_trace.py:359]
2025-06-03 14:38:09,231 INFO:   - 食谱: 黄米南瓜盅（朝阳区实验中学版） [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\food_trace.py:359]
2025-06-03 14:38:09,237 INFO:   - 食谱: 黑木耳炒山药（朝阳区实验中学版） [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\food_trace.py:359]
2025-06-03 14:38:09,245 INFO:   - 食谱: 鲜笋烧仔排（朝阳区实验中学版） [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\food_trace.py:359]
2025-06-03 14:38:09,253 INFO:   - 食谱: 鲜蚕豆烧大雁（朝阳区实验中学版） [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\food_trace.py:359]
2025-06-03 14:38:09,258 INFO:   - 食谱: 缤纷吐司蒸（朝阳区实验中学版） [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\food_trace.py:359]
2025-06-03 14:38:09,266 INFO: 食材一致性分析完成: 匹配率=0.0%, 缺失=9, 多余=0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\food_trace.py:551]
2025-06-03 14:38:45,344 INFO: 查询菜谱：日期=2025-06-03, 星期=1(0=周一), day_of_week=2, 餐次=早餐, 区域ID=42 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\food_trace.py:322]
