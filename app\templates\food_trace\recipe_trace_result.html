<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>菜品溯源信息</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: "Microsoft YaHei", Arial, sans-serif;
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
            min-height: 100vh;
            padding: 20px 0;
        }
        
        .trace-container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .trace-header {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .trace-header h1 {
            margin: 0;
            font-size: 2rem;
            font-weight: 600;
        }
        
        .trace-header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }
        
        .trace-content {
            padding: 30px;
        }
        
        .recipe-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 25px;
            text-align: center;
        }
        
        .recipe-name {
            font-size: 1.8rem;
            font-weight: 600;
            margin-bottom: 15px;
        }
        
        .recipe-details {
            display: flex;
            justify-content: space-around;
            flex-wrap: wrap;
            gap: 15px;
        }
        
        .recipe-detail {
            background: rgba(255,255,255,0.2);
            padding: 10px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
        }
        
        .ingredients-section {
            margin-top: 30px;
        }
        
        .section-title {
            color: #2c3e50;
            font-size: 1.4rem;
            font-weight: 600;
            margin-bottom: 20px;
            text-align: center;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        
        .ingredients-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        
        .ingredient-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border-left: 4px solid #3498db;
            transition: transform 0.2s ease;
        }
        
        .ingredient-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .ingredient-name {
            font-size: 1.2rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }
        
        .ingredient-name i {
            margin-right: 8px;
            color: #27ae60;
        }
        
        .ingredient-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }
        
        .info-item {
            display: flex;
            flex-direction: column;
        }
        
        .info-label {
            font-size: 0.8rem;
            color: #7f8c8d;
            font-weight: 500;
            margin-bottom: 2px;
        }
        
        .info-value {
            font-weight: 600;
            color: #2c3e50;
        }
        
        .supplier-highlight {
            background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
            color: white;
            padding: 8px 12px;
            border-radius: 15px;
            font-size: 0.9rem;
            text-align: center;
            margin-top: 10px;
        }
        
        .error-container {
            text-align: center;
            padding: 50px;
        }
        
        .error-icon {
            font-size: 4rem;
            color: #e74c3c;
            margin-bottom: 20px;
        }
        
        .stats-bar {
            background: #ecf0f1;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 25px;
            text-align: center;
        }
        
        .stats-item {
            display: inline-block;
            margin: 0 20px;
            color: #2c3e50;
        }
        
        .stats-number {
            font-size: 1.5rem;
            font-weight: 600;
            color: #3498db;
        }
        
        .qr-footer {
            background: #f8f9fa;
            padding: 20px;
            text-align: center;
            border-top: 1px solid #e9ecef;
        }
        
        .qr-footer small {
            color: #6c757d;
        }
        
        @media (max-width: 768px) {
            .trace-container {
                margin: 0 10px;
            }
            
            .trace-header {
                padding: 20px;
            }
            
            .trace-header h1 {
                font-size: 1.5rem;
            }
            
            .trace-content {
                padding: 20px;
            }
            
            .recipe-details {
                flex-direction: column;
                align-items: center;
            }
            
            .ingredients-grid {
                grid-template-columns: 1fr;
            }
            
            .ingredient-info {
                grid-template-columns: 1fr;
            }
            
            .stats-item {
                display: block;
                margin: 10px 0;
            }
        }
    </style>
</head>
<body>
    <div class="trace-container">
        <div class="trace-header">
            <h1><i class="fas fa-utensils"></i> 菜品溯源信息</h1>
            <p>扫描二维码查看菜品完整的食材供应链信息</p>
        </div>
        
        <div class="trace-content">
            {% if error %}
                <div class="error-container">
                    <div class="error-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <h3>溯源信息获取失败</h3>
                    <p class="text-muted">{{ error }}</p>
                </div>
            {% else %}
                <!-- 菜品基本信息 -->
                {% if recipe_info %}
                <div class="recipe-card">
                    <div class="recipe-name">{{ recipe_info.name }}</div>
                    <div class="recipe-details">
                        <div class="recipe-detail">
                            <i class="fas fa-calendar"></i> {{ recipe_info.meal_date }}
                        </div>
                        <div class="recipe-detail">
                            <i class="fas fa-clock"></i> {{ recipe_info.meal_type }}
                        </div>
                        <div class="recipe-detail">
                            <i class="fas fa-school"></i> {{ recipe_info.school }}
                        </div>
                        <div class="recipe-detail">
                            <i class="fas fa-tags"></i> {{ recipe_info.category or '未分类' }}
                        </div>
                    </div>
                </div>

                <!-- 统计信息 -->
                <div class="stats-bar">
                    <div class="stats-item">
                        <div class="stats-number">{{ recipe_info.ingredients_count }}</div>
                        <div>种食材</div>
                    </div>
                    <div class="stats-item">
                        <div class="stats-number">{{ ingredients_info|selectattr('supplier', 'ne', '未知')|list|length }}</div>
                        <div>个供应商</div>
                    </div>
                    <div class="stats-item">
                        <div class="stats-number">{{ ingredients_info|selectattr('batch_number', 'ne', '未知')|list|length }}</div>
                        <div>个批次</div>
                    </div>
                </div>
                {% endif %}

                <!-- 食材溯源信息 -->
                {% if ingredients_info and ingredients_info|length > 0 %}
                <div class="ingredients-section">
                    <h2 class="section-title">
                        <i class="fas fa-leaf"></i> 食材溯源信息
                    </h2>
                    <div class="ingredients-grid">
                        {% for ingredient in ingredients_info %}
                        <div class="ingredient-card">
                            <div class="ingredient-name">
                                <i class="fas fa-seedling"></i>
                                {{ ingredient.name }}
                            </div>
                            <div class="ingredient-info">
                                <div class="info-item">
                                    <span class="info-label">用量</span>
                                    <span class="info-value">{{ ingredient.quantity }}{{ ingredient.unit }}</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">批次号</span>
                                    <span class="info-value">{{ ingredient.batch_number }}</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">入库日期</span>
                                    <span class="info-value">{{ ingredient.stock_in_date }}</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">生产日期</span>
                                    <span class="info-value">{{ ingredient.production_date }}</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">过期日期</span>
                                    <span class="info-value">{{ ingredient.expiry_date }}</span>
                                </div>
                            </div>
                            {% if ingredient.supplier != '未知' %}
                            <div class="supplier-highlight">
                                <i class="fas fa-truck"></i> {{ ingredient.supplier }}
                            </div>
                            {% endif %}
                        </div>
                        {% endfor %}
                    </div>
                </div>
                {% else %}
                <div class="ingredients-section">
                    <h2 class="section-title">
                        <i class="fas fa-leaf"></i> 食材溯源信息
                    </h2>
                    <div class="text-center text-muted">
                        <i class="fas fa-info-circle fa-2x mb-3"></i>
                        <p>暂无食材溯源信息</p>
                    </div>
                </div>
                {% endif %}
            {% endif %}
        </div>
        
        <div class="qr-footer">
            <small>
                <i class="fas fa-shield-alt"></i>
                此信息由校园餐智慧食堂平台提供，确保菜品食材安全可追溯
            </small>
        </div>
    </div>
</body>
</html>
