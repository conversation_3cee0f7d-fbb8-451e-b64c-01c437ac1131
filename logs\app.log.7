2025-06-03 13:32:57,608 INFO: 使用库存记录: ID=70, 批次号=B20250602379197, 数量=199.70 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:895]
2025-06-03 13:32:57,611 INFO: 准备更新库存: ID=70, 减少数量=199.7 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:916]
2025-06-03 13:32:57,612 INFO: 使用库存记录: ID=71, 批次号=B202506027a72b5, 数量=100.00 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:895]
2025-06-03 13:32:57,612 INFO: 准备更新库存: ID=71, 减少数量=100.0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:916]
2025-06-03 13:32:57,617 INFO: 使用库存记录: ID=72, 批次号=B202506021cfe48, 数量=200.00 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:895]
2025-06-03 13:32:57,617 INFO: 准备更新库存: ID=72, 减少数量=200.0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:916]
2025-06-03 13:32:57,618 INFO: 使用库存记录: ID=73, 批次号=B2025060241bb6d, 数量=100.00 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:895]
2025-06-03 13:32:57,618 INFO: 准备更新库存: ID=73, 减少数量=100.0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:916]
2025-06-03 13:32:57,618 INFO: 使用库存记录: ID=74, 批次号=B20250602d76ba9, 数量=699.80 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:895]
2025-06-03 13:32:57,619 INFO: 准备更新库存: ID=74, 减少数量=699.8 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:916]
2025-06-03 13:32:57,619 WARNING: 库存不足: 食材=米饭, 需要=1000.0, 当前=699.8 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:826]
2025-06-03 13:32:57,622 INFO: 创建平账入库: 食材=米饭, 需要=1000.0, 当前=699.8, 差额=300.20000000000005, 批次号=BAL-20250603-72-6b9357 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\balance_stock_in.py:53]
2025-06-03 13:32:57,639 INFO: 创建新库存记录: 批次号=BAL-20250603-72-6b9357, 数量=300.20000000000005 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\balance_stock_in.py:170]
2025-06-03 13:32:57,647 INFO: 平账入库完成: 食材=米饭, 补充数量=300.20000000000005, 入库单ID=94 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\balance_stock_in.py:200]
2025-06-03 13:32:57,647 INFO: 平账入库成功: 食材=米饭, 补充数量=300.20000000000005, 批次号=BAL-20250603-72-6b9357 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:844]
2025-06-03 13:32:57,651 INFO: 平账入库后找到 1 条库存记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:889]
2025-06-03 13:32:57,651 INFO: 使用库存记录: ID=76, 批次号=BAL-20250603-72-6b9357, 数量=300.20 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:895]
2025-06-03 13:32:57,652 INFO: 准备更新库存: ID=76, 减少数量=1000.0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:916]
2025-06-03 13:32:57,657 INFO: 执行库存更新: 共 13 条记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:945]
2025-06-03 13:32:57,767 INFO: 当前用户: 18373062333 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:94]
2025-06-03 13:32:57,768 INFO: 用户区域ID: 42 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:95]
2025-06-03 13:32:57,769 INFO: 用户区域名称: 朝阳区实验中学 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:96]
2025-06-03 13:32:57,770 INFO: 是否管理员: 0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:97]
2025-06-03 13:33:16,778 INFO: 当前用户: 18373062333 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:94]
2025-06-03 13:33:16,783 INFO: 用户区域ID: 42 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:95]
2025-06-03 13:33:16,785 INFO: 用户区域名称: 朝阳区实验中学 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:96]
2025-06-03 13:33:16,785 INFO: 是否管理员: 0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:97]
2025-06-03 13:33:57,394 WARNING: Suspicious path blocked: /wp-admin/setup-config.php from 172.69.136.161 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:110]
2025-06-03 13:33:57,395 ERROR: Security middleware error: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:161]
2025-06-03 13:34:07,726 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-03 13:34:07,744 INFO: 成功获取 0 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-03 13:34:31,849 WARNING: Suspicious path blocked: /wordpress/wp-admin/setup-config.php from 172.71.172.38 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:110]
2025-06-03 13:34:31,850 ERROR: Security middleware error: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:161]
2025-06-03 13:43:09,223 WARNING: Suspicious path blocked: /.env from 196.251.86.163 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:110]
2025-06-03 13:43:09,223 ERROR: Security middleware error: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:161]
2025-06-03 13:57:47,717 WARNING: Suspicious path blocked: /.env from 196.251.73.96 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:110]
2025-06-03 13:57:47,717 ERROR: Security middleware error: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:161]
2025-06-03 14:03:01,721 WARNING: Suspicious path blocked: /.env from 196.251.73.96 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:110]
2025-06-03 14:03:01,721 ERROR: Security middleware error: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:161]
2025-06-03 14:35:12,989 INFO: 获取周菜单: area_id=42, week_start=2025-06-02, 类型=<class 'datetime.date'> [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:33]
2025-06-03 14:35:12,989 INFO: 使用日期字符串: 2025-06-02 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:51]
2025-06-03 14:35:12,990 INFO: 执行SQL: 
                SELECT TOP 1 id, area_id, week_start, week_end, status, created_by, created_at, updated_at
                FROM weekly_menus
                WHERE area_id = :area_id AND CONVERT(VARCHAR(10), week_start, 120) = :week_start_str
                 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:63]
2025-06-03 14:35:12,991 INFO: SQL参数: area_id=42, week_start_str=2025-06-02 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:64]
2025-06-03 14:35:12,995 INFO: SQL查询成功，找到菜单: id=37 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:70]
2025-06-03 14:35:12,996 INFO: 通过ID获取完整菜单对象成功: id=37 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:75]
2025-06-03 14:35:12,997 INFO: 获取周菜单: area_id=42, week_start=2025-06-02, 类型=<class 'datetime.date'> [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:33]
2025-06-03 14:35:12,997 INFO: 使用日期字符串: 2025-06-02 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:51]
2025-06-03 14:35:12,997 INFO: 执行SQL: 
                SELECT TOP 1 id, area_id, week_start, week_end, status, created_by, created_at, updated_at
                FROM weekly_menus
                WHERE area_id = :area_id AND CONVERT(VARCHAR(10), week_start, 120) = :week_start_str
                 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:63]
2025-06-03 14:35:12,998 INFO: SQL参数: area_id=42, week_start_str=2025-06-02 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:64]
