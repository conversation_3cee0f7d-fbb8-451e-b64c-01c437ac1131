2025-06-03 17:36:53,099 ERROR: 库存统计查询失败: (pyodbc.ProgrammingError) ('42000', '[42000] [Microsoft][ODBC SQL Server Driver][SQL Server]操作数数据类型 nvarchar(max) 对于 sum 运算符无效。 (8117) (SQLExecDirectW); [42000] [Microsoft][ODBC SQL Server Driver][SQL Server]无法预定义语句。 (8180)')
[SQL: 
        SELECT
            CAST(si.stock_in_date AS DATE) as date,
            COUNT(DISTINCT si.id) as stock_in_count,
            COUNT(sii.id) as stock_in_items,
            SUM(sii.quantity * sii.unit_price) as stock_in_amount,
            SUM(sii.quantity) as stock_in_quantity
        FROM stock_ins si
        JOIN stock_in_items sii ON si.id = sii.stock_in_id
        JOIN warehouses w ON si.warehouse_id = w.id
        JOIN ingredients i ON sii.ingredient_id = i.id
        WHERE w.area_id = 42 AND CAST(si.stock_in_date AS DATE) >= ? AND CAST(si.stock_in_date AS DATE) <= ?
        GROUP BY CAST(si.stock_in_date AS DATE)
        ORDER BY CAST(si.stock_in_date AS DATE)
    ]
[parameters: ('2025-05-04', '2025-06-03')]
(Background on this error at: https://sqlalche.me/e/20/f405) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:948]
2025-06-03 17:41:56,945 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:786]
2025-06-03 17:42:01,207 ERROR: 库存统计查询失败: (pyodbc.ProgrammingError) ('42000', '[42000] [Microsoft][ODBC SQL Server Driver][SQL Server]操作数数据类型 nvarchar(max) 对于 sum 运算符无效。 (8117) (SQLExecDirectW); [42000] [Microsoft][ODBC SQL Server Driver][SQL Server]无法预定义语句。 (8180)')
[SQL: 
        SELECT
            CAST(si.stock_in_date AS DATE) as date,
            COUNT(DISTINCT si.id) as stock_in_count,
            COUNT(sii.id) as stock_in_items,
            SUM(sii.quantity * sii.unit_price) as stock_in_amount,
            SUM(sii.quantity) as stock_in_quantity
        FROM stock_ins si
        JOIN stock_in_items sii ON si.id = sii.stock_in_id
        JOIN warehouses w ON si.warehouse_id = w.id
        JOIN ingredients i ON sii.ingredient_id = i.id
        WHERE w.area_id = 42 AND CAST(si.stock_in_date AS DATE) >= ? AND CAST(si.stock_in_date AS DATE) <= ?
        GROUP BY CAST(si.stock_in_date AS DATE)
        ORDER BY CAST(si.stock_in_date AS DATE)
    ]
[parameters: ('2025-05-04', '2025-06-03')]
(Background on this error at: https://sqlalche.me/e/20/f405) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:948]
2025-06-03 17:42:10,929 ERROR: 库存统计查询失败: (pyodbc.ProgrammingError) ('42000', '[42000] [Microsoft][ODBC SQL Server Driver][SQL Server]操作数数据类型 nvarchar(max) 对于 sum 运算符无效。 (8117) (SQLExecDirectW); [42000] [Microsoft][ODBC SQL Server Driver][SQL Server]无法预定义语句。 (8180)')
[SQL: 
        SELECT
            CAST(si.stock_in_date AS DATE) as date,
            COUNT(DISTINCT si.id) as stock_in_count,
            COUNT(sii.id) as stock_in_items,
            SUM(sii.quantity * sii.unit_price) as stock_in_amount,
            SUM(sii.quantity) as stock_in_quantity
        FROM stock_ins si
        JOIN stock_in_items sii ON si.id = sii.stock_in_id
        JOIN warehouses w ON si.warehouse_id = w.id
        JOIN ingredients i ON sii.ingredient_id = i.id
        WHERE w.area_id = 42 AND CAST(si.stock_in_date AS DATE) >= ? AND CAST(si.stock_in_date AS DATE) <= ?
        GROUP BY CAST(si.stock_in_date AS DATE)
        ORDER BY CAST(si.stock_in_date AS DATE)
    ]
[parameters: ('2025-05-04', '2025-06-03')]
(Background on this error at: https://sqlalche.me/e/20/f405) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:948]
2025-06-03 17:42:34,016 ERROR: 库存统计查询失败: (pyodbc.ProgrammingError) ('42000', '[42000] [Microsoft][ODBC SQL Server Driver][SQL Server]操作数数据类型 nvarchar(max) 对于 sum 运算符无效。 (8117) (SQLExecDirectW); [42000] [Microsoft][ODBC SQL Server Driver][SQL Server]无法预定义语句。 (8180)')
[SQL: 
        SELECT
            CAST(si.stock_in_date AS DATE) as date,
            COUNT(DISTINCT si.id) as stock_in_count,
            COUNT(sii.id) as stock_in_items,
            SUM(sii.quantity * sii.unit_price) as stock_in_amount,
            SUM(sii.quantity) as stock_in_quantity
        FROM stock_ins si
        JOIN stock_in_items sii ON si.id = sii.stock_in_id
        JOIN warehouses w ON si.warehouse_id = w.id
        JOIN ingredients i ON sii.ingredient_id = i.id
        WHERE w.area_id = 42 AND CAST(si.stock_in_date AS DATE) >= ? AND CAST(si.stock_in_date AS DATE) <= ? AND sii.storage_location_id = ?
        GROUP BY CAST(si.stock_in_date AS DATE)
        ORDER BY CAST(si.stock_in_date AS DATE)
    ]
[parameters: ('2025-05-04', '2025-06-03', 9)]
(Background on this error at: https://sqlalche.me/e/20/f405) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:948]
2025-06-03 17:42:41,584 ERROR: 库存统计查询失败: (pyodbc.ProgrammingError) ('42000', '[42000] [Microsoft][ODBC SQL Server Driver][SQL Server]操作数数据类型 nvarchar(max) 对于 sum 运算符无效。 (8117) (SQLExecDirectW); [42000] [Microsoft][ODBC SQL Server Driver][SQL Server]无法预定义语句。 (8180)')
[SQL: 
        SELECT
            CAST(si.stock_in_date AS DATE) as date,
            COUNT(DISTINCT si.id) as stock_in_count,
            COUNT(sii.id) as stock_in_items,
            SUM(sii.quantity * sii.unit_price) as stock_in_amount,
            SUM(sii.quantity) as stock_in_quantity
        FROM stock_ins si
        JOIN stock_in_items sii ON si.id = sii.stock_in_id
        JOIN warehouses w ON si.warehouse_id = w.id
        JOIN ingredients i ON sii.ingredient_id = i.id
        WHERE w.area_id = 42 AND CAST(si.stock_in_date AS DATE) >= ? AND CAST(si.stock_in_date AS DATE) <= ? AND sii.storage_location_id = ? AND i.category_id = ?
        GROUP BY CAST(si.stock_in_date AS DATE)
        ORDER BY CAST(si.stock_in_date AS DATE)
    ]
[parameters: ('2025-05-04', '2025-06-03', 9, 1)]
(Background on this error at: https://sqlalche.me/e/20/f405) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:948]
2025-06-03 17:44:26,947 ERROR: 库存统计查询失败: (pyodbc.ProgrammingError) ('42000', '[42000] [Microsoft][ODBC SQL Server Driver][SQL Server]操作数数据类型 nvarchar(max) 对于 sum 运算符无效。 (8117) (SQLExecDirectW); [42000] [Microsoft][ODBC SQL Server Driver][SQL Server]无法预定义语句。 (8180)')
[SQL: 
        SELECT
            CAST(si.stock_in_date AS DATE) as date,
            COUNT(DISTINCT si.id) as stock_in_count,
            COUNT(sii.id) as stock_in_items,
            SUM(sii.quantity * sii.unit_price) as stock_in_amount,
            SUM(sii.quantity) as stock_in_quantity
        FROM stock_ins si
        JOIN stock_in_items sii ON si.id = sii.stock_in_id
        JOIN warehouses w ON si.warehouse_id = w.id
        JOIN ingredients i ON sii.ingredient_id = i.id
        WHERE w.area_id = 42 AND CAST(si.stock_in_date AS DATE) >= ? AND CAST(si.stock_in_date AS DATE) <= ?
        GROUP BY CAST(si.stock_in_date AS DATE)
        ORDER BY CAST(si.stock_in_date AS DATE)
    ]
[parameters: ('2025-05-04', '2025-06-03')]
(Background on this error at: https://sqlalche.me/e/20/f405) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:948]
2025-06-03 17:44:53,416 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:786]
2025-06-03 17:45:02,707 ERROR: 库存统计查询失败: (pyodbc.ProgrammingError) ('42000', '[42000] [Microsoft][ODBC SQL Server Driver][SQL Server]操作数数据类型 nvarchar(max) 对于 sum 运算符无效。 (8117) (SQLExecDirectW); [42000] [Microsoft][ODBC SQL Server Driver][SQL Server]无法预定义语句。 (8180)')
[SQL: 
        SELECT
            CAST(si.stock_in_date AS DATE) as date,
            COUNT(DISTINCT si.id) as stock_in_count,
            COUNT(sii.id) as stock_in_items,
            SUM(sii.quantity * sii.unit_price) as stock_in_amount,
            SUM(sii.quantity) as stock_in_quantity
        FROM stock_ins si
        JOIN stock_in_items sii ON si.id = sii.stock_in_id
        JOIN warehouses w ON si.warehouse_id = w.id
        JOIN ingredients i ON sii.ingredient_id = i.id
        WHERE w.area_id = 42 AND CAST(si.stock_in_date AS DATE) >= ? AND CAST(si.stock_in_date AS DATE) <= ?
        GROUP BY CAST(si.stock_in_date AS DATE)
        ORDER BY CAST(si.stock_in_date AS DATE)
    ]
[parameters: ('2025-05-04', '2025-06-03')]
(Background on this error at: https://sqlalche.me/e/20/f405) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:948]
2025-06-03 17:45:39,265 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:786]
2025-06-03 17:46:58,634 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:786]
