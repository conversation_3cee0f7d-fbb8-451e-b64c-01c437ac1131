<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>标签打印测试</title>
    <style>
        body {
            font-family: "Microsoft YaHei", Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .label-preview {
            width: 60mm;
            height: 40mm;
            border: 2px solid #000;
            padding: 3mm;
            margin: 10px;
            display: inline-block;
            vertical-align: top;
            background: white;
            position: relative;
        }
        
        .ingredient-name {
            font-size: 14px;
            font-weight: bold;
            text-align: center;
            border-bottom: 1px solid #333;
            padding-bottom: 2mm;
            margin-bottom: 2mm;
        }
        
        .label-info {
            font-size: 10px;
            line-height: 1.3;
        }
        
        .label-info-row {
            margin-bottom: 1mm;
        }
        
        .qr-code {
            position: absolute;
            right: 3mm;
            top: 15mm;
            width: 15mm;
            height: 15mm;
            border: 1px solid #ddd;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 8px;
            text-align: center;
        }
        
        .btn {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn:hover {
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>入库标签打印功能测试</h1>
        
        <div class="mb-3">
            <a href="/stock-in" class="btn btn-primary">返回入库单列表</a>
            <button onclick="window.print()" class="btn btn-success">打印预览</button>
        </div>
        
        <h2>标签样式预览</h2>
        <p>以下是标签的实际尺寸预览（60mm x 40mm）：</p>
        
        <!-- 示例标签1 -->
        <div class="label-preview">
            <div class="ingredient-name">大白菜</div>
            <div class="label-info">
                <div class="label-info-row"><strong>批次:</strong> B20250127001</div>
                <div class="label-info-row"><strong>数量:</strong> 50kg</div>
                <div class="label-info-row"><strong>供应商:</strong> 绿色农场</div>
                <div class="label-info-row"><strong>电话:</strong> 138-0000-0000</div>
                <div class="label-info-row"><strong>过期:</strong> 2025-02-15</div>
            </div>
            <div class="qr-code">溯源<br>二维码</div>
        </div>
        
        <!-- 示例标签2 -->
        <div class="label-preview">
            <div class="ingredient-name">土豆</div>
            <div class="label-info">
                <div class="label-info-row"><strong>批次:</strong> B20250127002</div>
                <div class="label-info-row"><strong>数量:</strong> 30kg</div>
                <div class="label-info-row"><strong>供应商:</strong> 山区合作社</div>
                <div class="label-info-row"><strong>电话:</strong> 139-1111-1111</div>
                <div class="label-info-row"><strong>过期:</strong> 2025-03-01</div>
            </div>
            <div class="qr-code">溯源<br>二维码</div>
        </div>
        
        <!-- 示例标签3 -->
        <div class="label-preview">
            <div class="ingredient-name">胡萝卜</div>
            <div class="label-info">
                <div class="label-info-row"><strong>批次:</strong> B20250127003</div>
                <div class="label-info-row"><strong>数量:</strong> 25kg</div>
                <div class="label-info-row"><strong>供应商:</strong> 有机蔬菜基地</div>
                <div class="label-info-row"><strong>电话:</strong> 137-2222-2222</div>
                <div class="label-info-row"><strong>过期:</strong> 2025-02-28</div>
            </div>
            <div class="qr-code">溯源<br>二维码</div>
        </div>
        
        <h2>功能说明</h2>
        <ul>
            <li><strong>标签尺寸</strong>：60mm x 40mm（适合小型标签打印机）</li>
            <li><strong>A4纸布局</strong>：每行3个标签，每页15个标签</li>
            <li><strong>包含信息</strong>：食材名称、批次号、数量、供应商、电话、过期日期</li>
            <li><strong>溯源二维码</strong>：包含完整的食材溯源信息</li>
            <li><strong>打印优化</strong>：黑白打印友好，边框清晰</li>
        </ul>
        
        <h2>使用方法</h2>
        <ol>
            <li>在入库单详情页面，点击"批量打印标签"按钮打印所有食材标签</li>
            <li>选中特定食材，点击"打印选中标签"按钮打印选中的食材标签</li>
            <li>在每个食材行的操作列，点击打印按钮打印单个食材标签</li>
            <li>打印时建议使用A4纸，选择实际尺寸打印</li>
        </ol>
        
        <h2>二维码内容</h2>
        <p>每个标签的二维码包含以下溯源信息：</p>
        <ul>
            <li>食材名称和ID</li>
            <li>批次号</li>
            <li>入库单ID和日期</li>
            <li>供应商信息</li>
            <li>生产日期和过期日期</li>
        </ul>
        
        <div class="mt-4">
            <a href="/stock-in" class="btn btn-primary">返回入库单列表</a>
        </div>
    </div>
</body>
</html>
