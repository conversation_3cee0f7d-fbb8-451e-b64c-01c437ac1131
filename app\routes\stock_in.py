from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, current_app, abort, session
from flask_login import login_required, current_user
from app.models import StockIn, StockInItem, Warehouse, Inventory, Ingredient, StorageLocation, Supplier, PurchaseOrder, PurchaseOrderItem, StockInDocument, IngredientInspection, SupplierSchoolRelation
from app import db, csrf
from datetime import datetime, date, timedelta
from sqlalchemy import text, func
import json
import uuid
import os
from werkzeug.utils import secure_filename
from sqlalchemy.orm import joinedload
from sqlalchemy.exc import SQLAlchemyError

# 配置上传文件存储路径
UPLOAD_FOLDER = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'static', 'uploads', 'stock_in_docs')
ALLOWED_EXTENSIONS = {'pdf', 'png', 'jpg', 'jpeg', 'doc', 'docx', 'xls', 'xlsx'}

# 确保上传目录存在
os.makedirs(UPLOAD_FOLDER, exist_ok=True)

stock_in_bp = Blueprint('stock_in', __name__)

# Helper function to get accessible area IDs
def get_accessible_area_ids():
    accessible_areas = current_user.get_accessible_areas()
    return [area.id for area in accessible_areas]

def check_can_cancel_stock_in_operation(stock_in):
    """检查是否可以撤销入库操作"""
    if stock_in.status != '已入库':
        return False

    # 检查是否有库存被消费计划引用
    try:
        check_sql = text("""
            SELECT COUNT(*) FROM consumption_details cd
            JOIN inventories i ON cd.inventory_id = i.id
            JOIN stock_in_items sii ON i.ingredient_id = sii.ingredient_id
                AND i.batch_number = sii.batch_number
            WHERE sii.stock_in_id = :stock_in_id
        """)
        ref_count = db.session.execute(check_sql, {'stock_in_id': stock_in.id}).scalar()
        return ref_count == 0
    except:
        return False

def check_can_cancel_order(stock_in):
    """检查是否可以取消入库单"""
    if stock_in.status not in ['待审核', '已审核']:
        return False

    # 待审核状态总是可以取消
    if stock_in.status == '待审核':
        return True

    # 已审核状态需要检查是否有库存被引用
    if stock_in.status == '已审核':
        return check_can_cancel_stock_in_operation(stock_in)

    return True

def check_can_approve(stock_in):
    """检查是否可以审核通过"""
    if stock_in.status != '已入库':
        return False

    # 检查是否有入库明细
    items_count = StockInItem.query.filter_by(stock_in_id=stock_in.id).count()
    return items_count > 0

def get_stock_in_consumption_status(stock_in_id):
    """获取入库单的消耗状态信息"""
    try:
        from sqlalchemy import func, cast, Numeric

        # 查询入库单的所有明细
        stock_in_items = StockInItem.query.filter_by(stock_in_id=stock_in_id).all()

        if not stock_in_items:
            return {
                'total_items': 0,
                'consumed_items': 0,
                'remaining_items': 0,
                'consumption_rate': 0,
                'status': 'no_items'
            }

        total_items = len(stock_in_items)
        consumed_items = 0
        remaining_items = 0
        total_in_quantity = 0
        total_remaining_quantity = 0

        for item in stock_in_items:
            # 入库数量
            in_quantity = float(item.quantity) if item.quantity else 0
            total_in_quantity += in_quantity

            # 查询该批次在当前库存中的剩余数量
            remaining_inventory = Inventory.query.filter_by(
                ingredient_id=item.ingredient_id,
                batch_number=item.batch_number,
                status='正常'
            ).first()

            remaining_quantity = 0
            if remaining_inventory and remaining_inventory.quantity:
                try:
                    remaining_quantity = float(remaining_inventory.quantity)
                except (ValueError, TypeError):
                    remaining_quantity = 0

            total_remaining_quantity += remaining_quantity

            # 判断该批次是否已消耗完
            if remaining_quantity <= 0:
                consumed_items += 1
            else:
                remaining_items += 1

        # 计算消耗率（基于数量）
        if total_in_quantity > 0:
            quantity_consumption_rate = round(((total_in_quantity - total_remaining_quantity) / total_in_quantity) * 100, 1)
        else:
            quantity_consumption_rate = 0

        # 计算批次消耗率
        batch_consumption_rate = round((consumed_items / total_items) * 100, 1) if total_items > 0 else 0

        # 使用数量消耗率作为主要指标
        consumption_rate = quantity_consumption_rate

        # 确定状态
        if consumption_rate == 0:
            status = 'not_consumed'  # 未消耗
        elif consumption_rate < 50:
            status = 'partially_consumed'  # 部分消耗
        elif consumption_rate < 100:
            status = 'mostly_consumed'  # 大部分消耗
        else:
            status = 'fully_consumed'  # 完全消耗

        return {
            'total_items': total_items,
            'consumed_items': consumed_items,
            'remaining_items': remaining_items,
            'consumption_rate': consumption_rate,
            'total_in_quantity': total_in_quantity,
            'total_remaining_quantity': total_remaining_quantity,
            'status': status
        }

    except Exception as e:
        current_app.logger.error(f"计算入库单消耗状态时出错: {str(e)}")
        return {
            'total_items': 0,
            'consumed_items': 0,
            'remaining_items': 0,
            'consumption_rate': 0,
            'status': 'error'
        }

@stock_in_bp.route('/stock-in')
@login_required
def index():
    """入库单列表页面"""
    # 获取当前用户可访问的区域
    accessible_areas = current_user.get_accessible_areas()
    area_ids = [area.id for area in accessible_areas]

    # 获取查询参数
    page = request.args.get('page', 1, type=int)
    per_page = current_app.config['ITEMS_PER_PAGE']
    status = request.args.get('status', '')
    start_date = request.args.get('start_date', '')
    end_date = request.args.get('end_date', '')

    # 构建查询
    query = StockIn.query.join(Warehouse).filter(Warehouse.area_id.in_(area_ids))

    # 应用过滤条件
    if status:
        query = query.filter(StockIn.status == status)
    if start_date:
        query = query.filter(StockIn.stock_in_date >= datetime.strptime(start_date, '%Y-%m-%d'))
    if end_date:
        query = query.filter(StockIn.stock_in_date <= datetime.strptime(end_date + ' 23:59:59', "%Y-%m-%d %H:%M"))

    # 按创建时间降序排序
    query = query.order_by(StockIn.created_at.desc())

    # 分页
    pagination = query.paginate(page=page, per_page=per_page, error_out=0)
    stock_ins = pagination.items

    # 为每个入库单计算消耗状态
    stock_in_consumption_status = {}
    for stock_in in stock_ins:
        if stock_in.status == '已入库':
            # 查询该入库单的所有食材在当前库存中的状态
            consumption_info = get_stock_in_consumption_status(stock_in.id)
            stock_in_consumption_status[stock_in.id] = consumption_info

    return render_template('stock_in/index.html',
                          stock_ins=stock_ins,
                          pagination=pagination,
                          status=status,
                          start_date=start_date,
                          end_date=end_date,
                          stock_in_consumption_status=stock_in_consumption_status)

@stock_in_bp.route('/stock-in/create', methods=['GET', 'POST'])
@login_required
def create():
    """创建入库单"""
    # 获取当前用户可访问的区域
    accessible_areas = current_user.get_accessible_areas()
    area_ids = [area.id for area in accessible_areas]

    if request.method == 'POST':
        # 获取表单数据
        warehouse_id = request.form.get('warehouse_id', type=int)
        stock_in_date = request.form.get('stock_in_date')
        stock_in_type = request.form.get('stock_in_type')
        supplier_id = request.form.get('supplier_id', type=int)
        notes = request.form.get('notes')

        # 验证数据
        if not warehouse_id or not stock_in_date or not stock_in_type:
            flash('请填写所有必填字段', 'danger')
            return redirect(url_for('stock_in.create'))

        # 检查用户是否有权限操作该仓库
        warehouse = Warehouse.query.get_or_404(warehouse_id)
        if not current_user.can_access_area_by_id(warehouse.area_id):
            flash('您没有权限操作该仓库', 'danger')
            return redirect(url_for('stock_in.index'))

        # 生成入库单号
        stock_in_number = f"RK{datetime.now().strftime('%Y%m%d%H%M%S')}"

        try:
            # 使用原始SQL语句创建入库单，避免ORM处理datetime字段
            sql = text("""
            INSERT INTO stock_ins (stock_in_number, warehouse_id, stock_in_date, stock_in_type, operator_id, status, notes)
            OUTPUT inserted.id
            VALUES (:stock_in_number, :warehouse_id, :stock_in_date, :stock_in_type, :operator_id, :status, :notes)
            """)

            # 执行SQL语句
            result = db.session.execute(sql, {
                'stock_in_number': stock_in_number,
                'warehouse_id': warehouse_id,
                'stock_in_date': datetime.strptime(stock_in_date, '%Y-%m-%d'),
                'stock_in_type': stock_in_type,
                'operator_id': current_user.id,
                'status': '待审核',
                'notes': notes
            })

            # 获取新创建的入库单ID
            stock_in_id = result.fetchone()[0]

            # 提交事务
            db.session.commit()

            # 获取创建的入库单对象
            stock_in = StockIn.query.get(stock_in_id)

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"创建入库单时出错: {str(e)}")
            flash(f'创建入库单时出错: {str(e)}', 'danger')
            return redirect(url_for('stock_in.create'))

        flash('入库单创建成功，请添加入库明细', 'success')
        return redirect(url_for('stock_in.batch_editor_simplified', id=stock_in.id))

    # GET请求，显示创建表单
    # 获取仓库列表
    warehouses = Warehouse.query.filter(Warehouse.area_id.in_(area_ids), Warehouse.status == '正常').all()

    # 获取供应商列表，根据用户权限筛选
    if current_user.is_admin():
        # 系统管理员可以看到所有供应商
        suppliers = Supplier.query.filter_by(status=1).all()
    else:
        # 普通用户只能看到与自己管辖学校有合作关系的供应商
        # 通过供应商-学校关联表筛选供应商
        suppliers = Supplier.query.join(SupplierSchoolRelation)\
                    .filter(SupplierSchoolRelation.area_id.in_(area_ids))\
                    .filter(Supplier.status == 1)\
                    .filter(SupplierSchoolRelation.status == 1)\
                    .distinct().all()

    # 获取所有可用的采购订单列表（不区分状态，简化流程）
    purchase_orders = PurchaseOrder.query.filter(
        PurchaseOrder.area_id.in_(area_ids)
    ).order_by(PurchaseOrder.created_at.desc()).all()

    # 记录日志，帮助调试
    current_app.logger.info(f"用户 {current_user.username} 访问入库单创建页面，可访问区域IDs: {area_ids}")
    current_app.logger.info(f"找到 {len(purchase_orders)} 个可用的采购订单")

    return render_template('stock_in/form.html',
                          stock_in=None,
                          warehouses=warehouses,
                          suppliers=suppliers,
                          purchase_orders=purchase_orders,
                          title='创建入库单')

@stock_in_bp.route('/stock-in/<int:id>')
@login_required
def view(id):
    """查看入库单详情 - 使用传统视图"""
    stock_in = StockIn.query.get_or_404(id)

    # 检查用户是否有权限查看
    if not current_user.can_access_area_by_id(stock_in.warehouse.area_id):
        flash('您没有权限查看该入库单', 'danger')
        return redirect(url_for('stock_in.index'))

    # 使用传统视图
    return redirect(url_for('stock_in.view_details', id=id))

@stock_in_bp.route('/stock-in/<int:id>/details')
@login_required
def view_details(id):
    """查看入库单详情（传统视图）"""
    stock_in = StockIn.query.get_or_404(id)

    # 检查用户是否有权限查看
    if not current_user.can_access_area_by_id(stock_in.warehouse.area_id):
        flash('您没有权限查看该入库单', 'danger')
        return redirect(url_for('stock_in.index'))

    # 获取入库明细
    stock_in_items = StockInItem.query.filter_by(stock_in_id=id).all()

    # 获取已上传的文档列表
    documents = StockInDocument.query.filter_by(stock_in_id=id).all()

    # 获取供应商列表（用于上传文档时选择）
    suppliers = Supplier.query.filter(Supplier.status == 1).all()

    # 检查各种操作的可用性
    can_cancel_stock_in = check_can_cancel_stock_in_operation(stock_in)
    can_cancel_order = check_can_cancel_order(stock_in)
    can_approve = check_can_approve(stock_in)

    return render_template('stock_in/view.html',
                          stock_in=stock_in,
                          stock_in_items=stock_in_items,
                          documents=documents,
                          suppliers=suppliers,
                          can_cancel_stock_in=can_cancel_stock_in,
                          can_cancel_order=can_cancel_order,
                          can_approve=can_approve)

@stock_in_bp.route('/stock-in/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit(id):
    """编辑入库单 - 重定向到简化版批次编辑器"""
    stock_in = StockIn.query.get_or_404(id)

    # 检查用户是否有权限编辑
    if not current_user.can_access_area_by_id(stock_in.warehouse.area_id):
        flash('您没有权限编辑该入库单', 'danger')
        return redirect(url_for('stock_in.index'))

    # 只有待审核状态的入库单可以编辑
    if stock_in.status != '待审核':
        flash('只有待审核状态的入库单可以编辑', 'warning')
        return redirect(url_for('stock_in.view', id=id))

    # 直接重定向到简化版批次编辑器
    return redirect(url_for('stock_in.batch_editor_simplified', id=id))

@stock_in_bp.route('/stock-in/<int:id>/add-item', methods=['POST'])
@login_required
def add_item(id):
    """添加入库明细"""
    stock_in = StockIn.query.get_or_404(id)

    # 检查用户是否有权限编辑
    if not current_user.can_access_area_by_id(stock_in.warehouse.area_id):
        flash('您没有权限编辑该入库单', 'danger')
        return redirect(url_for('stock_in.index'))

    # 只有待审核状态的入库单可以添加明细
    if stock_in.status != '待审核':
        flash('只有待审核状态的入库单可以添加明细', 'warning')
        return redirect(url_for('stock_in.view', id=id))

    # 获取表单数据
    ingredient_id = request.form.get('ingredient_id', type=int)
    storage_location_id = request.form.get('storage_location_id', type=int)
    batch_number = request.form.get('batch_number')
    quantity = request.form.get('quantity', type=float)
    unit = request.form.get('unit')
    production_date = request.form.get('production_date')
    expiry_date = request.form.get('expiry_date')
    # 不使用 unit_price 字段，因为数据库表中没有这个字段
    notes = request.form.get('notes')

    # 验证数据
    if not ingredient_id or not storage_location_id or not batch_number or not quantity or not unit or not production_date or not expiry_date:
        flash('请填写所有必填字段', 'danger')
        return redirect(url_for('stock_in.edit', id=id))

    try:
        # 使用原始SQL语句创建入库明细，避免ORM处理datetime字段
        unit_price = request.form.get('unit_price', type=float)

        sql = text("""
        INSERT INTO stock_in_items (stock_in_id, ingredient_id, storage_location_id, batch_number, quantity, unit,
                                   production_date, expiry_date, unit_price, notes, quality_status, created_at, updated_at)
        OUTPUT inserted.id
        VALUES (:stock_in_id, :ingredient_id, :storage_location_id, :batch_number, :quantity, :unit,
                CONVERT(DATE, :production_date, 23), CONVERT(DATE, :expiry_date, 23), :unit_price, :notes, :quality_status, GETDATE(), GETDATE())
        """)

        # 执行SQL语句
        result = db.session.execute(sql, {
            'stock_in_id': id,
            'ingredient_id': ingredient_id,
            'storage_location_id': storage_location_id,
            'batch_number': batch_number,
            'quantity': quantity,
            'unit': unit,
            'production_date': production_date,  # 直接使用字符串格式的日期
            'expiry_date': expiry_date,  # 直接使用字符串格式的日期
            'unit_price': unit_price,
            'notes': notes,
            'quality_status': '良好'  # 默认质量状态
        })

        # 获取新创建的入库明细ID
        stock_in_item_id = result.fetchone()[0]

        # 提交事务
        db.session.commit()

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"添加入库明细时出错: {str(e)}")
        flash(f'添加入库明细时出错: {str(e)}', 'danger')
        return redirect(url_for('stock_in.edit', id=id))

    flash('入库明细添加成功', 'success')
    return redirect(url_for('stock_in.batch_editor_simplified', id=id))

@stock_in_bp.route('/stock-in/<int:id>/remove-item/<int:item_id>', methods=['POST'])
@login_required
def remove_item(id, item_id):
    """删除入库明细"""
    stock_in = StockIn.query.get_or_404(id)
    stock_in_item = StockInItem.query.get_or_404(item_id)

    # 检查用户是否有权限编辑
    if not current_user.can_access_area_by_id(stock_in.warehouse.area_id):
        flash('您没有权限编辑该入库单', 'danger')
        return redirect(url_for('stock_in.index'))

    # 只有待审核状态的入库单可以删除明细
    if stock_in.status != '待审核':
        flash('只有待审核状态的入库单可以删除明细', 'warning')
        return redirect(url_for('stock_in.view', id=id))

    # 检查明细是否属于该入库单
    if stock_in_item.stock_in_id != id:
        flash('该明细不属于当前入库单', 'danger')
        return redirect(url_for('stock_in.edit', id=id))

    try:
        # 使用原始SQL语句删除入库明细
        sql = text("""
        DELETE FROM stock_in_items
        WHERE id = :id
        """)

        # 执行SQL语句
        db.session.execute(sql, {'id': item_id})

        # 提交事务
        db.session.commit()

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"删除入库明细时出错: {str(e)}")
        flash(f'删除入库明细时出错: {str(e)}', 'danger')
        return redirect(url_for('stock_in.edit', id=id))

    flash('入库明细删除成功', 'success')
    return redirect(url_for('stock_in.batch_editor_simplified', id=id))

@stock_in_bp.route('/stock-in/<int:id>/approve', methods=['POST'])
@login_required
def approve(id):
    """审核入库单"""
    stock_in = StockIn.query.get_or_404(id)

    # 检查用户是否有权限审核
    if not current_user.can_access_area_by_id(stock_in.warehouse.area_id):
        flash('您没有权限审核该入库单', 'danger')
        return redirect(url_for('stock_in.index'))

    # 只有待审核状态的入库单可以审核
    if stock_in.status != '待审核':
        flash('只有待审核状态的入库单可以审核', 'warning')
        return redirect(url_for('stock_in.view', id=id))

    # 检查是否有入库明细
    if StockInItem.query.filter_by(stock_in_id=id).count() == 0:
        flash('入库单没有明细，无法审核', 'warning')
        return redirect(url_for('stock_in.batch_editor_simplified', id=id))

    # 获取选中的项目（如果是从表单提交过来的）
    selected_items = request.form.getlist('selected_items[]')

    try:
        # 使用原始SQL语句更新入库单状态
        sql = text("""
        UPDATE stock_ins
        SET status = :status, inspector_id = :inspector_id
        WHERE id = :id
        """)

        # 执行SQL语句
        db.session.execute(sql, {
            'status': '已审核',
            'inspector_id': current_user.id,
            'id': id
        })

        # 提交事务
        db.session.commit()

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"审核入库单时出错: {str(e)}")
        flash(f'审核入库单时出错: {str(e)}', 'danger')
        return redirect(url_for('stock_in.view', id=id))

    flash('入库单审核成功', 'success')
    return redirect(url_for('stock_in.batch_editor_simplified', id=id))

@stock_in_bp.route('/stock-in/<int:id>/stock-in-operation', methods=['POST'])
@login_required
def stock_in_operation(id):
    """入库操作 - 将数据写入学校库存系统"""
    stock_in = StockIn.query.get_or_404(id)

    # 检查用户是否有权限操作
    if not current_user.can_access_area_by_id(stock_in.warehouse.area_id):
        return jsonify({
            'success': False,
            'message': '您没有权限操作该入库单'
        })

    # 只有待审核状态的入库单可以入库
    if stock_in.status != '待审核':
        return jsonify({
            'success': False,
            'message': '只有待审核状态的入库单可以进行入库操作'
        })

    # 检查是否有入库明细
    items = StockInItem.query.filter_by(stock_in_id=id).all()
    if not items:
        return jsonify({
            'success': False,
            'message': '入库单没有明细，无法进行入库操作'
        })

    try:
        # 写入学校库存系统
        for item in items:
            # 查找现有库存
            existing_inventory = Inventory.query.filter_by(
                warehouse_id=stock_in.warehouse_id,
                ingredient_id=item.ingredient_id,
                batch_number=item.batch_number,
                status='正常'
            ).first()

            if existing_inventory:
                # 使用原始SQL语句更新现有库存
                update_sql = text("""
                UPDATE inventories
                SET quantity = quantity + :quantity,
                    updated_at = GETDATE()
                WHERE id = :id
                """)

                db.session.execute(update_sql, {
                    'quantity': item.quantity,
                    'id': existing_inventory.id
                })
            else:
                # 使用原始SQL语句创建新库存记录
                insert_sql = text("""
                INSERT INTO inventories (warehouse_id, storage_location_id, ingredient_id, batch_number,
                                        quantity, unit, production_date, expiry_date, supplier_id,
                                        status, notes, created_at, updated_at)
                VALUES (:warehouse_id, :storage_location_id, :ingredient_id, :batch_number,
                        :quantity, :unit, CONVERT(DATE, :production_date, 23), CONVERT(DATE, :expiry_date, 23), :supplier_id,
                        :status, :notes, GETDATE(), GETDATE())
                """)

                db.session.execute(insert_sql, {
                    'warehouse_id': stock_in.warehouse_id,
                    'storage_location_id': item.storage_location_id,
                    'ingredient_id': item.ingredient_id,
                    'batch_number': item.batch_number,
                    'quantity': item.quantity,
                    'unit': item.unit,
                    'production_date': item.production_date.strftime('%Y-%m-%d') if item.production_date else None,
                    'expiry_date': item.expiry_date.strftime('%Y-%m-%d') if item.expiry_date else None,
                    'supplier_id': item.supplier_id,
                    'status': '正常',
                    'notes': f'由入库单 {stock_in.stock_in_number} 创建'
                })

        # 使用原始SQL语句更新入库单状态为"已入库"
        update_stock_in_sql = text("""
        UPDATE stock_ins
        SET status = :status,
            updated_at = GETDATE()
        WHERE id = :id
        """)

        db.session.execute(update_stock_in_sql, {
            'status': '已入库',
            'id': id
        })

        # 提交事务
        db.session.commit()

        return jsonify({
            'success': True,
            'message': f'入库操作成功！已将 {len(items)} 个批次的食材数据写入学校库存系统'
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"入库操作时出错: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'入库操作时出错: {str(e)}'
        })

@stock_in_bp.route('/stock-in/<int:id>/cancel-stock-in-operation', methods=['POST'])
@login_required
def cancel_stock_in_operation(id):
    """撤销入库操作 - 从学校库存系统中移除相关记录"""
    stock_in = StockIn.query.get_or_404(id)

    # 检查用户是否有权限操作
    if not current_user.can_access_area_by_id(stock_in.warehouse.area_id):
        return jsonify({
            'success': False,
            'message': '您没有权限操作该入库单'
        })

    # 只有已入库状态的入库单可以撤销入库
    if stock_in.status != '已入库':
        return jsonify({
            'success': False,
            'message': '只有已入库状态的入库单可以撤销入库操作'
        })

    # 检查是否有入库明细
    items = StockInItem.query.filter_by(stock_in_id=id).all()
    if not items:
        return jsonify({
            'success': False,
            'message': '入库单没有明细，无法撤销入库操作'
        })

    try:
        # 从学校库存系统中移除相关记录
        for item in items:
            # 查找对应的库存记录
            inventory = Inventory.query.filter_by(
                warehouse_id=stock_in.warehouse_id,
                ingredient_id=item.ingredient_id,
                batch_number=item.batch_number,
                status='正常'
            ).first()

            if inventory:
                if inventory.quantity >= item.quantity:
                    # 使用原始SQL语句减少库存数量
                    update_sql = text("""
                    UPDATE inventories
                    SET quantity = quantity - :quantity,
                        updated_at = GETDATE()
                    WHERE id = :id
                    """)

                    db.session.execute(update_sql, {
                        'quantity': item.quantity,
                        'id': inventory.id
                    })

                    # 如果库存数量变为0，检查是否被引用后再删除
                    if inventory.quantity == item.quantity:
                        # 检查是否被消费计划引用
                        check_ref_sql = text("""
                        SELECT COUNT(*) FROM consumption_details
                        WHERE inventory_id = :id
                        """)
                        ref_count = db.session.execute(check_ref_sql, {'id': inventory.id}).scalar()

                        if ref_count > 0:
                            # 如果被引用，只将数量设为0，不删除记录
                            zero_sql = text("""
                            UPDATE inventories
                            SET quantity = 0, status = '已用完', updated_at = GETDATE()
                            WHERE id = :id
                            """)
                            db.session.execute(zero_sql, {'id': inventory.id})
                        else:
                            # 如果没有被引用，可以安全删除
                            delete_sql = text("""
                            DELETE FROM inventories WHERE id = :id
                            """)
                            db.session.execute(delete_sql, {'id': inventory.id})
                else:
                    return jsonify({
                        'success': False,
                        'message': f'库存数量不足，无法撤销入库。食材：{item.ingredient.name}，批次：{item.batch_number}'
                    })

        # 使用原始SQL语句更新入库单状态为"待审核"
        update_stock_in_sql = text("""
        UPDATE stock_ins
        SET status = :status,
            updated_at = GETDATE()
        WHERE id = :id
        """)

        db.session.execute(update_stock_in_sql, {
            'status': '待审核',
            'id': id
        })

        # 提交事务
        db.session.commit()

        return jsonify({
            'success': True,
            'message': f'撤销入库操作成功！已从学校库存系统中移除 {len(items)} 个批次的食材记录'
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"撤销入库操作时出错: {str(e)}")

        # 提供更友好的错误信息
        error_message = str(e)
        if 'FK_consumption_details_inventories' in error_message:
            friendly_message = '无法撤销入库操作：该批次食材已被消费计划使用，请先删除相关的消费计划后再试。'
        elif 'REFERENCE 约束' in error_message or 'FOREIGN KEY' in error_message:
            friendly_message = '无法撤销入库操作：该批次食材已被其他记录引用，请先处理相关记录后再试。'
        elif 'DELETE 语句' in error_message:
            friendly_message = '无法删除库存记录：该批次食材正在被使用中，请检查相关的消费计划或出库记录。'
        else:
            friendly_message = f'撤销入库操作时出错: {error_message}'

        return jsonify({
            'success': False,
            'message': friendly_message
        })

@stock_in_bp.route('/stock-in/<int:id>/batch-editor')
@login_required
def batch_editor(id):
    """批次编辑器 - 重定向到简化版批次编辑器"""
    return redirect(url_for('stock_in.batch_editor_simplified', id=id))

@stock_in_bp.route('/stock-in/<int:id>/batch-editor-simplified')
@login_required
def batch_editor_simplified(id):
    """简化版批次编辑器 - 一页完成所有操作，支持只读模式"""
    stock_in = StockIn.query.get_or_404(id)

    # 检查用户是否有权限查看
    if not current_user.can_access_area_by_id(stock_in.warehouse.area_id):
        flash('您没有权限查看该入库单', 'danger')
        return redirect(url_for('stock_in.index'))

    # 获取所有入库明细
    stock_in_items = StockInItem.query.filter_by(stock_in_id=id).all()



    # 检查入库单状态
    mode = 'edit'  # 始终使用编辑模式
    if stock_in.status != '待审核':
        flash('只有待审核状态的入库单可以编辑，请谨慎操作', 'warning')

    # 获取供应商列表，根据用户权限筛选
    if current_user.is_admin():
        # 系统管理员可以看到所有供应商
        suppliers = Supplier.query.filter_by(status=1).all()
    else:
        # 普通用户只能看到与自己管辖学校有合作关系的供应商
        accessible_area_ids = [area.id for area in current_user.get_accessible_areas()]

        # 通过供应商-学校关联表筛选供应商
        suppliers = Supplier.query.join(SupplierSchoolRelation)\
                    .filter(SupplierSchoolRelation.area_id.in_(accessible_area_ids))\
                    .filter(Supplier.status == 1)\
                    .filter(SupplierSchoolRelation.status == 1)\
                    .distinct().all()

    # 获取存储位置列表
    storage_locations = StorageLocation.query.filter_by(warehouse_id=stock_in.warehouse_id, status='正常').all()

    # 获取当前日期和30天后的日期（用于默认值）
    now = datetime.now().date()

    # 获取已上传的文档列表
    documents = StockInDocument.query.filter_by(stock_in_id=id).all()

    return render_template('stock_in/batch_editor_simplified.html',
                          stock_in=stock_in,
                          stock_in_items=stock_in_items,
                          suppliers=suppliers,
                          storage_locations=storage_locations,
                          documents=documents,
                          now=now,
                          timedelta=timedelta,
                          mode=mode)

@stock_in_bp.route('/stock-in/<int:id>/batch-editor/step1')
@login_required
def batch_editor_step1(id):
    """批次编辑器 - 步骤1：基本信息"""
    stock_in = StockIn.query.get_or_404(id)

    # 检查用户是否有权限编辑
    if not current_user.can_access_area_by_id(stock_in.warehouse.area_id):
        flash('您没有权限编辑该入库单', 'danger')
        return redirect(url_for('stock_in.index'))

    # 只有待审核状态的入库单可以编辑
    if stock_in.status != '待审核':
        flash('只有待审核状态的入库单可以编辑', 'warning')
        return redirect(url_for('stock_in.view', id=id))

    # 获取入库明细
    stock_in_items = StockInItem.query.filter_by(stock_in_id=id).all()

    # 获取供应商列表，根据用户权限筛选
    if current_user.is_admin():
        # 系统管理员可以看到所有供应商
        suppliers = Supplier.query.filter_by(status=1).all()
    else:
        # 普通用户只能看到与自己管辖学校有合作关系的供应商
        accessible_area_ids = [area.id for area in current_user.get_accessible_areas()]

        # 通过供应商-学校关联表筛选供应商
        suppliers = Supplier.query.join(SupplierSchoolRelation)\
                    .filter(SupplierSchoolRelation.area_id.in_(accessible_area_ids))\
                    .filter(Supplier.status == 1)\
                    .filter(SupplierSchoolRelation.status == 1)\
                    .distinct().all()

    # 获取存储位置列表
    storage_locations = StorageLocation.query.filter_by(warehouse_id=stock_in.warehouse_id, status='正常').all()

    # 获取当前日期和30天后的日期（用于默认值）
    now = datetime.now().date()

    return render_template('stock_in/batch_editor_step1.html',
                          stock_in=stock_in,
                          stock_in_items=stock_in_items,
                          suppliers=suppliers,
                          storage_locations=storage_locations,
                          now=now,
                          timedelta=timedelta)

@stock_in_bp.route('/stock-in/<int:stock_in_id>/save-batch-edit-simplified', methods=['POST'])
@login_required
def save_batch_edit_simplified(stock_in_id):
    """保存简化版批次编辑器的修改"""
    stock_in = StockIn.query.get_or_404(stock_in_id)

    # 检查是否是AJAX请求
    is_ajax = request.headers.get('X-Requested-With') == 'XMLHttpRequest'

    # 检查用户是否有权限编辑
    if not current_user.can_access_area_by_id(stock_in.warehouse.area_id):
        if is_ajax:
            return jsonify({'success': False, 'message': '您没有权限编辑该入库单'})
        flash('您没有权限编辑该入库单', 'danger')
        return redirect(url_for('stock_in.index'))

    # 只有待审核状态的入库单可以编辑
    if stock_in.status != '待审核':
        if is_ajax:
            return jsonify({'success': False, 'message': '只有待审核状态的入库单可以编辑'})
        flash('只有待审核状态的入库单可以编辑', 'warning')
        return redirect(url_for('stock_in.view', id=stock_in_id))

    # 获取选中的项目
    selected_items = request.form.getlist('selected_items[]')

    # 调试日志：打印所有表单数据
    current_app.logger.info(f"批次编辑器保存 - 入库单ID: {stock_in_id}")
    current_app.logger.info(f"选中的项目: {selected_items}")
    for key, value in request.form.items():
        if 'unit_price' in key:
            current_app.logger.info(f"单价字段 - {key}: {value}")

    if not selected_items:
        if is_ajax:
            return jsonify({'success': False, 'message': '请至少选择一个批次进行入库'})
        flash('请至少选择一个批次进行入库', 'warning')
        return redirect(url_for('stock_in.batch_editor_simplified', id=stock_in_id))

    # 检查是否需要直接审核
    direct_approve = request.form.get('direct_approve') == 'on'

    try:
        # 更新每个选中的项目
        for item_id in selected_items:
            item_id = int(item_id)
            item = StockInItem.query.get(item_id)

            if item and item.stock_in_id == stock_in_id:
                # 获取表单数据
                supplier_id = request.form.get(f'supplier_id_{item_id}')
                storage_location_id = request.form.get(f'storage_location_id_{item_id}')
                quantity = request.form.get(f'quantity_{item_id}')
                unit_price = request.form.get(f'unit_price_{item_id}')
                production_date = request.form.get(f'production_date_{item_id}')
                expiry_date = request.form.get(f'expiry_date_{item_id}')

                # 调试日志：打印每个项目的详细信息
                current_app.logger.info(f"项目 {item_id} 详细信息:")
                current_app.logger.info(f"  - 供应商ID: {supplier_id}")
                current_app.logger.info(f"  - 存储位置ID: {storage_location_id}")
                current_app.logger.info(f"  - 数量: {quantity}")
                current_app.logger.info(f"  - 单价: {unit_price}")
                current_app.logger.info(f"  - 生产日期: {production_date}")
                current_app.logger.info(f"  - 过期日期: {expiry_date}")

                # 验证数量和单价不为0
                try:
                    quantity_float = float(quantity)
                    unit_price_float = float(unit_price)

                    if quantity_float <= 0 or unit_price_float <= 0:
                        error_msg = f'食材 "{item.ingredient.name}" 的数量或单价不能为0'
                        if is_ajax:
                            return jsonify({'success': False, 'message': error_msg})
                        flash(error_msg, 'warning')
                        return redirect(url_for('stock_in.batch_editor_simplified', id=stock_in_id))
                except (ValueError, TypeError):
                    error_msg = f'食材 "{item.ingredient.name}" 的数量或单价格式不正确'
                    if is_ajax:
                        return jsonify({'success': False, 'message': error_msg})
                    flash(error_msg, 'warning')
                    return redirect(url_for('stock_in.batch_editor_simplified', id=stock_in_id))

                # 使用原始SQL语句更新入库明细
                sql = text("""
                UPDATE stock_in_items
                SET supplier_id = :supplier_id,
                    storage_location_id = :storage_location_id,
                    quantity = :quantity,
                    unit_price = :unit_price,
                    production_date = CONVERT(DATE, :production_date, 23),
                    expiry_date = CONVERT(DATE, :expiry_date, 23),
                    updated_at = GETDATE()
                WHERE id = :id
                """)

                # 执行SQL语句
                db.session.execute(sql, {
                    'supplier_id': supplier_id if supplier_id else None,
                    'storage_location_id': storage_location_id if storage_location_id else None,
                    'quantity': quantity,
                    'unit_price': unit_price,
                    'production_date': production_date,
                    'expiry_date': expiry_date,
                    'id': item_id
                })

                # 处理文件上传
                documents = request.files.getlist(f'documents_{item_id}[]')
                if documents and documents[0].filename:
                    for document in documents:
                        if document and document.filename:
                            # 生成安全的文件名
                            filename = secure_filename(document.filename)
                            # 生成唯一的文件名
                            unique_filename = f"{uuid.uuid4().hex}_{filename}"
                            # 确定文件保存路径
                            file_path = os.path.join(current_app.config['UPLOAD_FOLDER'], 'stock_in_documents', unique_filename)
                            # 确保目录存在
                            os.makedirs(os.path.dirname(file_path), exist_ok=True)
                            # 保存文件
                            document.save(file_path)

                            # 创建文档记录
                            doc = StockInDocument(
                                stock_in_id=stock_in_id,
                                document_type='检验检疫证明',
                                file_path=file_path,
                                supplier_id=supplier_id if supplier_id else None,
                                notes=f"上传者: {current_user.username}"
                            )

                            # 添加文档后，需要关联到入库明细
                            db.session.add(doc)
                            db.session.flush()  # 获取文档ID

                            # 添加到多对多关系
                            doc.items.append(item)

        # 提交事务
        db.session.commit()
        success_msg = f'成功更新 {len(selected_items)} 个批次的信息'

        if is_ajax:
            # 如果是AJAX请求，返回JSON响应
            if direct_approve:
                # 如果选择了直接审核，返回重定向URL
                return jsonify({
                    'success': True,
                    'message': success_msg,
                    'redirect': url_for('stock_in.approve', id=stock_in_id)
                })
            else:
                # 否则返回成功消息和重定向到详情页面
                return jsonify({
                    'success': True,
                    'message': success_msg,
                    'redirect': url_for('stock_in.view_details', id=stock_in_id)
                })
        else:
            # 如果不是AJAX请求，使用传统的重定向方式
            flash(success_msg, 'success')

            # 如果选择了直接审核，则执行审核操作
            if direct_approve:
                return redirect(url_for('stock_in.approve', id=stock_in_id))
            else:
                # 否则重定向到入库单详情页面
                return redirect(url_for('stock_in.view_details', id=stock_in_id))

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"批量更新入库明细时出错: {str(e)}")
        error_msg = f'批量更新入库明细时出错: {str(e)}'

        if is_ajax:
            return jsonify({'success': False, 'message': error_msg})

        flash(error_msg, 'danger')

    # 如果是AJAX请求，这里不会执行到，因为已经在上面返回了JSON响应
    # 如果不是AJAX请求，返回传统的重定向到详情页面
    return redirect(url_for('stock_in.view_details', id=stock_in_id))

@stock_in_bp.route('/stock-in/<int:stock_in_id>/save-batch-edit/step1', methods=['POST'])
@login_required
def save_batch_edit_step1(stock_in_id):
    """保存批次编辑器步骤1的修改并进入步骤2"""
    stock_in = StockIn.query.get_or_404(stock_in_id)

    # 检查用户是否有权限编辑
    if not current_user.can_access_area_by_id(stock_in.warehouse.area_id):
        flash('您没有权限编辑该入库单', 'danger')
        return redirect(url_for('stock_in.index'))

    # 只有待审核状态的入库单可以编辑
    if stock_in.status != '待审核':
        flash('只有待审核状态的入库单可以编辑', 'warning')
        return redirect(url_for('stock_in.view', id=stock_in_id))

    # 获取选中的项目
    selected_items = request.form.getlist('selected_items[]')

    if not selected_items:
        flash('请至少选择一个批次进行编辑', 'warning')
        return redirect(url_for('stock_in.batch_editor_step1', id=stock_in_id))

    try:
        # 更新每个选中的项目
        for item_id in selected_items:
            item_id = int(item_id)
            item = StockInItem.query.get(item_id)

            if item and item.stock_in_id == stock_in_id:
                # 获取表单数据
                supplier_id = request.form.get(f'supplier_id_{item_id}')
                storage_location_id = request.form.get(f'storage_location_id_{item_id}')
                quantity = request.form.get(f'quantity_{item_id}')
                unit_price = request.form.get(f'unit_price_{item_id}')

                # 使用原始SQL语句更新入库明细
                sql = text("""
                UPDATE stock_in_items
                SET supplier_id = :supplier_id,
                    storage_location_id = :storage_location_id,
                    quantity = :quantity,
                    unit_price = :unit_price,
                    updated_at = GETDATE()
                WHERE id = :id
                """)

                # 执行SQL语句
                db.session.execute(sql, {
                    'supplier_id': supplier_id if supplier_id else None,
                    'storage_location_id': storage_location_id if storage_location_id else None,
                    'quantity': quantity,
                    'unit_price': unit_price,
                    'id': item_id
                })

        # 提交事务
        db.session.commit()
        flash(f'成功更新 {len(selected_items)} 个批次的基本信息，请继续完成日期与单据绑定', 'success')

        # 将选中的项目ID存储在会话中，以便在步骤2中使用
        session['selected_batch_items'] = selected_items

        # 重定向到步骤2
        return redirect(url_for('stock_in.batch_editor_step2', id=stock_in_id))

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"批量更新入库明细基本信息时出错: {str(e)}")
        flash(f'批量更新入库明细基本信息时出错: {str(e)}', 'danger')
        return redirect(url_for('stock_in.batch_editor_step1', id=stock_in_id))

@stock_in_bp.route('/stock-in/<int:id>/batch-editor/step2')
@login_required
def batch_editor_step2(id):
    """批次编辑器 - 步骤2：日期与单据"""
    stock_in = StockIn.query.get_or_404(id)

    # 检查用户是否有权限编辑
    if not current_user.can_access_area_by_id(stock_in.warehouse.area_id):
        flash('您没有权限编辑该入库单', 'danger')
        return redirect(url_for('stock_in.index'))

    # 只有待审核状态的入库单可以编辑
    if stock_in.status != '待审核':
        flash('只有待审核状态的入库单可以编辑', 'warning')
        return redirect(url_for('stock_in.view', id=id))

    # 获取入库明细
    stock_in_items = StockInItem.query.filter_by(stock_in_id=id).all()

    # 获取当前日期和30天后的日期（用于默认值）
    now = datetime.now().date()

    return render_template('stock_in/batch_editor_step2.html',
                          stock_in=stock_in,
                          stock_in_items=stock_in_items,
                          now=now,
                          timedelta=timedelta)

@stock_in_bp.route('/stock-in/<int:stock_in_id>/save-batch-edit/step2', methods=['POST'])
@login_required
def save_batch_edit_step2(stock_in_id):
    """保存批次编辑器步骤2的修改并完成整个流程"""
    stock_in = StockIn.query.get_or_404(stock_in_id)

    # 检查用户是否有权限编辑
    if not current_user.can_access_area_by_id(stock_in.warehouse.area_id):
        flash('您没有权限编辑该入库单', 'danger')
        return redirect(url_for('stock_in.index'))

    # 只有待审核状态的入库单可以编辑
    if stock_in.status != '待审核':
        flash('只有待审核状态的入库单可以编辑', 'warning')
        return redirect(url_for('stock_in.view', id=stock_in_id))

    # 获取选中的项目
    selected_items = request.form.getlist('selected_items[]')

    if not selected_items:
        flash('请至少选择一个批次进行编辑', 'warning')
        return redirect(url_for('stock_in.batch_editor_step2', id=stock_in_id))

    try:
        # 更新每个选中的项目
        for item_id in selected_items:
            item_id = int(item_id)
            item = StockInItem.query.get(item_id)

            if item and item.stock_in_id == stock_in_id:
                # 获取表单数据
                production_date = request.form.get(f'production_date_{item_id}')
                expiry_date = request.form.get(f'expiry_date_{item_id}')

                # 使用原始SQL语句更新入库明细的日期信息
                sql = text("""
                UPDATE stock_in_items
                SET production_date = CONVERT(DATE, :production_date, 23),
                    expiry_date = CONVERT(DATE, :expiry_date, 23),
                    updated_at = GETDATE()
                WHERE id = :id
                """)

                # 执行SQL语句
                db.session.execute(sql, {
                    'production_date': production_date,
                    'expiry_date': expiry_date,
                    'id': item_id
                })

                # 处理文件上传
                documents = request.files.getlist(f'documents_{item_id}[]')
                if documents and documents[0].filename:
                    for document in documents:
                        if document and document.filename:
                            # 生成安全的文件名
                            filename = secure_filename(document.filename)
                            # 生成唯一的文件名
                            unique_filename = f"{uuid.uuid4().hex}_{filename}"
                            # 确定文件保存路径
                            file_path = os.path.join(current_app.config['UPLOAD_FOLDER'], 'stock_in_documents', unique_filename)
                            # 确保目录存在
                            os.makedirs(os.path.dirname(file_path), exist_ok=True)
                            # 保存文件
                            document.save(file_path)

                            # 创建文档记录
                            doc = StockInDocument(
                                stock_in_item_id=item_id,
                                file_name=filename,
                                file_path=file_path,
                                document_type='检验检疫证明',
                                uploaded_by=current_user.id,
                                uploaded_at=datetime.now()
                            )
                            db.session.add(doc)

        # 提交事务
        db.session.commit()
        flash(f'成功更新 {len(selected_items)} 个批次的日期与单据信息', 'success')

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"批量更新入库明细日期与单据时出错: {str(e)}")
        flash(f'批量更新入库明细日期与单据时出错: {str(e)}', 'danger')

    return redirect(url_for('stock_in.batch_editor_simplified', id=stock_in_id))

@stock_in_bp.route('/stock-in/delete-batch-document', methods=['POST'])
@login_required
def delete_batch_document():
    """删除批次编辑器中的入库单据"""
    document_id = request.form.get('document_id')

    if not document_id:
        return jsonify({'success': False, 'message': '未提供文档ID'})

    try:
        document = StockInDocument.query.get(document_id)

        if not document:
            return jsonify({'success': False, 'message': '文档不存在'})

        # 检查用户是否有权限删除
        stock_in = StockIn.query.get(document.stock_in_id)
        if not stock_in:
            return jsonify({'success': False, 'message': '关联的入库单不存在'})

        if not current_user.can_access_area_by_id(stock_in.warehouse.area_id):
            return jsonify({'success': False, 'message': '您没有权限删除该文档'})

        # 只有待审核状态的入库单可以编辑
        if stock_in.status != '待审核':
            return jsonify({'success': False, 'message': '只有待审核状态的入库单可以编辑'})

        # 删除文件
        if os.path.exists(document.file_path):
            os.remove(document.file_path)

        # 删除数据库记录
        db.session.delete(document)
        db.session.commit()

        return jsonify({'success': True})

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"删除入库单据时出错: {str(e)}")
        return jsonify({'success': False, 'message': str(e)})

@stock_in_bp.route('/stock-in/<int:stock_in_id>/bulk-upload-documents', methods=['POST'])
@login_required
def bulk_upload_documents(stock_in_id):
    """批量上传入库单据"""
    stock_in = StockIn.query.get_or_404(stock_in_id)

    # 检查用户是否有权限编辑
    if not current_user.can_access_area_by_id(stock_in.warehouse.area_id):
        return jsonify({'success': False, 'message': '您没有权限编辑该入库单'})

    # 只有待审核状态的入库单可以编辑
    if stock_in.status != '待审核':
        return jsonify({'success': False, 'message': '只有待审核状态的入库单可以编辑'})

    # 获取选中的项目
    item_ids = request.form.getlist('item_ids[]')

    if not item_ids:
        return jsonify({'success': False, 'message': '请至少选择一个批次'})

    # 获取上传的文件
    documents = request.files.getlist('documents[]')

    if not documents or not documents[0].filename:
        return jsonify({'success': False, 'message': '请选择至少一个文件'})

    try:
        for item_id in item_ids:
            item_id = int(item_id)
            item = StockInItem.query.get(item_id)

            if item and item.stock_in_id == stock_in_id:
                for document in documents:
                    if document and document.filename:
                        # 生成安全的文件名
                        filename = secure_filename(document.filename)
                        # 生成唯一的文件名
                        unique_filename = f"{uuid.uuid4().hex}_{filename}"
                        # 确定文件保存路径
                        file_path = os.path.join(current_app.config['UPLOAD_FOLDER'], 'stock_in_documents', unique_filename)
                        # 确保目录存在
                        os.makedirs(os.path.dirname(file_path), exist_ok=True)
                        # 保存文件
                        document.save(file_path)

                        # 创建文档记录
                        doc = StockInDocument(
                            stock_in_id=stock_in_id,
                            document_type='检验检疫证明',
                            file_path=file_path,
                            supplier_id=item.supplier_id,
                            notes=f"批量上传 - 上传者: {current_user.username}"
                        )

                        # 添加文档后，需要关联到入库明细
                        db.session.add(doc)
                        db.session.flush()  # 获取文档ID

                        # 添加到多对多关系
                        doc.items.append(item)

        # 提交事务
        db.session.commit()
        return jsonify({'success': True})

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"批量上传入库单据时出错: {str(e)}")
        return jsonify({'success': False, 'message': str(e)})

@stock_in_bp.route('/stock-in/<int:id>/execute', methods=['GET', 'POST'])
@login_required
def execute(id):
    """执行入库单（更新库存）"""
    stock_in = StockIn.query.get_or_404(id)

    # 检查用户是否有权限执行
    if not current_user.can_access_area_by_id(stock_in.warehouse.area_id):
        flash('您没有权限执行该入库单', 'danger')
        return redirect(url_for('stock_in.index'))

    # 已入库或已取消状态的入库单不能再次执行
    if stock_in.status in ['已入库', '已取消']:
        flash(f'当前入库单状态为"{stock_in.status}"，不能执行入库操作', 'warning')
        return redirect(url_for('stock_in.batch_editor_simplified', id=id))

    # 记录日志，提示用户当前入库单状态
    current_app.logger.info(f"用户 {current_user.username} 正在执行状态为 {stock_in.status} 的入库单 {id}")
    if stock_in.status != '已审核':
        flash(f'注意：当前入库单状态为"{stock_in.status}"，正常流程应先审核再入库，但系统允许直接入库', 'info')

    # 获取入库明细
    stock_in_items = StockInItem.query.filter_by(stock_in_id=id).all()

    try:
        # 更新库存
        for item in stock_in_items:
            # 检查是否已存在相同批次的库存
            existing_inventory = Inventory.query.filter_by(
                warehouse_id=stock_in.warehouse_id,
                ingredient_id=item.ingredient_id,
                batch_number=item.batch_number,
                status='正常'
            ).first()

            if existing_inventory:
                # 使用原始SQL语句更新现有库存
                update_sql = text("""
                UPDATE inventories
                SET quantity = quantity + :quantity
                WHERE id = :id
                """)

                db.session.execute(update_sql, {
                    'quantity': item.quantity,
                    'id': existing_inventory.id
                })
            else:
                # 使用原始SQL语句创建新库存记录
                insert_sql = text("""
                INSERT INTO inventories (warehouse_id, storage_location_id, ingredient_id, batch_number,
                                        quantity, unit, production_date, expiry_date, supplier_id,
                                        status, notes, created_at, updated_at)
                VALUES (:warehouse_id, :storage_location_id, :ingredient_id, :batch_number,
                        :quantity, :unit, CONVERT(DATE, :production_date, 23), CONVERT(DATE, :expiry_date, 23), :supplier_id,
                        :status, :notes, GETDATE(), GETDATE())
                """)

                # 将日期对象转换为字符串格式
                production_date_str = item.production_date.strftime('%Y-%m-%d') if item.production_date else None
                expiry_date_str = item.expiry_date.strftime('%Y-%m-%d') if item.expiry_date else None

                db.session.execute(insert_sql, {
                    'warehouse_id': stock_in.warehouse_id,
                    'storage_location_id': item.storage_location_id,
                    'ingredient_id': item.ingredient_id,
                    'batch_number': item.batch_number,
                    'quantity': item.quantity,
                    'unit': item.unit,
                    'production_date': production_date_str,
                    'expiry_date': expiry_date_str,
                    'supplier_id': item.supplier_id,
                    'status': '正常',
                    'notes': f'由入库单 {stock_in.stock_in_number} 创建'
                })

        # 使用原始SQL语句更新入库单状态
        update_stock_in_sql = text("""
        UPDATE stock_ins
        SET status = :status
        WHERE id = :id
        """)

        db.session.execute(update_stock_in_sql, {
            'status': '已入库',
            'id': id
        })

        # 提交事务
        db.session.commit()

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"执行入库单时出错: {str(e)}")
        flash(f'执行入库单时出错: {str(e)}', 'danger')
        return redirect(url_for('stock_in.view', id=id))

    flash('入库单执行成功，库存已更新', 'success')
    return redirect(url_for('stock_in.batch_editor_simplified', id=id))

@stock_in_bp.route('/stock-in/<int:id>/cancel', methods=['POST'])
@login_required
def cancel(id):
    """取消入库单"""
    stock_in = StockIn.query.get_or_404(id)

    # 检查用户是否有权限取消
    if not current_user.can_access_area_by_id(stock_in.warehouse.area_id):
        flash('您没有权限取消该入库单', 'danger')
        return redirect(url_for('stock_in.index'))

    # 只有待审核或已审核状态的入库单可以取消
    if stock_in.status not in ['待审核', '已审核']:
        flash('只有待审核或已审核状态的入库单可以取消', 'warning')
        return redirect(url_for('stock_in.view', id=id))

    try:
        # 使用原始SQL语句更新入库单状态
        sql = text("""
        UPDATE stock_ins
        SET status = :status
        WHERE id = :id
        """)

        # 执行SQL语句
        db.session.execute(sql, {
            'status': '已取消',
            'id': id
        })

        # 提交事务
        db.session.commit()

        flash('入库单已取消', 'success')
        return redirect(url_for('stock_in.view', id=id))

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"取消入库单时出错: {str(e)}")
        flash(f'取消入库单时出错: {str(e)}', 'danger')
        return redirect(url_for('stock_in.view', id=id))

@stock_in_bp.route('/stock-in/<int:id>/delete', methods=['POST'])
@login_required
def delete_stock_in(id):
    """删除入库单"""
    stock_in = StockIn.query.get_or_404(id)

    # 检查用户是否有权限删除
    if not current_user.can_access_area_by_id(stock_in.warehouse.area_id):
        return jsonify({
            'success': False,
            'message': '您没有权限删除该入库单'
        })

    # 只有待审核状态且未实际入库的入库单可以删除
    if stock_in.status != '待审核':
        return jsonify({
            'success': False,
            'message': '只有待审核状态的入库单可以删除'
        })

    try:
        # 保存入库单信息用于审计日志
        stock_in_info = {
            'id': stock_in.id,
            'stock_in_number': stock_in.stock_in_number,
            'warehouse_id': stock_in.warehouse_id,
            'warehouse_name': stock_in.warehouse.name if stock_in.warehouse else None,
            'purchase_order_id': stock_in.purchase_order_id,
            'purchase_order_number': stock_in.purchase_order.order_number if stock_in.purchase_order else None,
            'stock_in_date': stock_in.stock_in_date.strftime('%Y-%m-%d') if stock_in.stock_in_date else None,
            'stock_in_type': stock_in.stock_in_type,
            'status': stock_in.status,
            'notes': stock_in.notes
        }

        # 先删除入库明细
        StockInItem.query.filter_by(stock_in_id=id).delete()

        # 删除入库单据
        StockInDocument.query.filter_by(stock_in_id=id).delete()

        # 删除入库单
        db.session.delete(stock_in)

        # 记录审计日志
        from app.utils.log_activity import log_activity
        log_activity(
            action='delete',
            resource_type='StockIn',
            resource_id=id,
            area_id=stock_in.warehouse.area_id,
            details=stock_in_info
        )

        # 提交事务
        db.session.commit()

        return jsonify({
            'success': True,
            'message': '入库单删除成功'
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'删除入库单失败: {str(e)}')

        # 提供更友好的错误信息
        error_message = str(e)
        if 'FK_consumption_details_inventories' in error_message:
            friendly_message = '无法删除入库单：该入库单的食材已被消费计划使用，请先删除相关的消费计划。'
        elif 'FK_stock_out_items_inventories' in error_message:
            friendly_message = '无法删除入库单：该入库单的食材已被出库单使用，请先处理相关的出库记录。'
        elif 'REFERENCE 约束' in error_message or 'FOREIGN KEY' in error_message:
            friendly_message = '无法删除入库单：该入库单已被其他记录引用，请先处理相关记录。'
        else:
            friendly_message = f'删除入库单失败: {error_message}'

        return jsonify({
            'success': False,
            'message': friendly_message
        })

@stock_in_bp.route('/stock-in/<int:id>/unapprove', methods=['POST'])
@login_required
def unapprove(id):
    """取消审核入库单"""
    stock_in = StockIn.query.get_or_404(id)

    # 检查用户是否有权限取消审核
    if not current_user.can_access_area_by_id(stock_in.warehouse.area_id):
        flash('您没有权限取消审核该入库单', 'danger')
        return redirect(url_for('stock_in.index'))

    # 只有已审核状态的入库单可以取消审核
    if stock_in.status != '已审核':
        flash('只有已审核状态的入库单可以取消审核', 'warning')
        return redirect(url_for('stock_in.view', id=id))

    try:
        # 使用原始SQL语句更新入库单状态
        sql = text("""
        UPDATE stock_ins
        SET status = :status, inspector_id = NULL
        WHERE id = :id
        """)

        # 执行SQL语句
        db.session.execute(sql, {
            'status': '待审核',
            'id': id
        })

        # 提交事务
        db.session.commit()

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"取消审核入库单时出错: {str(e)}")
        flash(f'取消审核入库单时出错: {str(e)}', 'danger')
        return redirect(url_for('stock_in.view', id=id))

    flash('入库单审核已取消，状态已改为待审核', 'success')
    return redirect(url_for('stock_in.view', id=id))

@stock_in_bp.route('/stock-in/<int:id>/print')
@login_required
def print_stock_in(id):
    """打印入库单"""
    stock_in = StockIn.query.get_or_404(id)

    # 检查用户是否有权限查看
    if not current_user.can_access_area_by_id(stock_in.warehouse.area_id):
        flash('您没有权限查看该入库单', 'danger')
        return redirect(url_for('stock_in.index'))

    # 获取入库明细
    stock_in_items = StockInItem.query.filter_by(stock_in_id=id).all()

    # 获取当前时间
    current_time = datetime.now()

    return render_template('stock_in/print.html',
                          stock_in=stock_in,
                          stock_in_items=stock_in_items,
                          current_time=current_time)

@stock_in_bp.route('/stock-in/create-from-purchase/<int:purchase_order_id>', methods=['GET', 'POST'])
@login_required
def create_from_purchase(purchase_order_id):
    """从采购订单创建入库单（旧版本）"""
    # 获取采购订单
    purchase_order = PurchaseOrder.query.get_or_404(purchase_order_id)

    # 获取该区域的默认仓库
    warehouse = Warehouse.query.filter_by(area_id=purchase_order.area_id, status='正常').first()

    if not warehouse:
        flash('未找到可用的仓库，请先创建仓库', 'danger')
        return redirect(url_for('warehouse.create'))

    if request.method == 'POST':
        # 获取表单数据
        stock_in_date = request.form.get('stock_in_date')
        notes = request.form.get('notes', '')

        # 验证数据
        if not stock_in_date:
            flash('请填写入库日期', 'danger')
            return redirect(url_for('stock_in.create_from_purchase', purchase_order_id=purchase_order_id))

        try:
            # 生成入库单号
            stock_in_number = f"RK{datetime.now().strftime('%Y%m%d%H%M%S')}"

            # 使用原始SQL语句创建入库单，避免ORM处理datetime字段
            sql = text("""
            INSERT INTO stock_ins (stock_in_number, warehouse_id, purchase_order_id, stock_in_date, stock_in_type, operator_id, status, notes)
            OUTPUT inserted.id
            VALUES (:stock_in_number, :warehouse_id, :purchase_order_id, :stock_in_date, :stock_in_type, :operator_id, :status, :notes)
            """)

            # 执行SQL语句
            result = db.session.execute(sql, {
                'stock_in_number': stock_in_number,
                'warehouse_id': warehouse.id,
                'purchase_order_id': purchase_order_id,
                'stock_in_date': stock_in_date,  # 直接使用字符串格式的日期
                'stock_in_type': '采购入库',
                'operator_id': current_user.id,
                'status': '待审核',
                'notes': f"从采购订单 {purchase_order.order_number} 创建\n{notes}"
            })

            # 获取新创建的入库单ID
            stock_in_id = result.fetchone()[0]

            # 获取采购订单明细
            purchase_items = PurchaseOrderItem.query.filter_by(order_id=purchase_order_id).all()

            # 获取默认存储位置
            default_storage_location = StorageLocation.query.filter_by(
                warehouse_id=warehouse.id,
                status='正常'
            ).first()

            if not default_storage_location:
                db.session.rollback()
                flash('未找到可用的存储位置，请先创建存储位置', 'danger')
                return redirect(url_for('storage_location.create', warehouse_id=warehouse.id))

            # 创建入库明细
            for item in purchase_items:
                # 生成批次号
                batch_number = f"B{datetime.now().strftime('%Y%m%d')}{uuid.uuid4().hex[:6]}"

                # 计算默认的生产日期和过期日期
                production_date = datetime.now().date()
                # 默认保质期为30天，如果食材有保质期信息则使用食材的保质期
                shelf_life = item.ingredient.shelf_life or 30
                expiry_date = production_date + timedelta(days=shelf_life)

                # 使用原始SQL语句创建入库明细
                insert_item_sql = text("""
                INSERT INTO stock_in_items (stock_in_id, ingredient_id, purchase_order_item_id, storage_location_id,
                                          batch_number, quantity, unit, unit_price, production_date, expiry_date,
                                          supplier_id, quality_status, notes)
                VALUES (:stock_in_id, :ingredient_id, :purchase_order_item_id, :storage_location_id,
                        :batch_number, :quantity, :unit, :unit_price, :production_date, :expiry_date,
                        :supplier_id, :quality_status, :notes)
                """)

                # 将日期对象转换为字符串格式
                production_date_str = production_date.strftime('%Y-%m-%d')
                expiry_date_str = expiry_date.strftime('%Y-%m-%d')

                db.session.execute(insert_item_sql, {
                    'stock_in_id': stock_in_id,
                    'ingredient_id': item.ingredient_id,
                    'purchase_order_item_id': item.id,
                    'storage_location_id': default_storage_location.id,
                    'batch_number': batch_number,
                    'quantity': item.quantity,
                    'unit': item.unit,
                    'unit_price': item.unit_price,
                    'production_date': production_date_str,
                    'expiry_date': expiry_date_str,
                    'supplier_id': purchase_order.supplier_id,
                    'quality_status': '良好',
                    'notes': "从采购订单明细创建"
                })

            # 提交事务
            db.session.commit()

            flash('入库单创建成功，请完善食材信息', 'success')
            return redirect(url_for('stock_in.batch_editor_simplified', id=stock_in_id))

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"从采购订单创建入库单时出错: {str(e)}")
            flash(f'从采购订单创建入库单时出错: {str(e)}', 'danger')
            return redirect(url_for('stock_in.create_from_purchase', purchase_order_id=purchase_order_id))

    # GET请求，显示创建表单
    return render_template('stock_in/create_from_purchase.html',
                          purchase_order=purchase_order,
                          warehouse=warehouse,
                          title='从采购订单创建入库单')

@stock_in_bp.route('/stock-in/create-from-purchase-order/<int:order_id>', methods=['GET'])
@login_required
def create_from_purchase_order(order_id):
    """从采购订单创建入库单（新版本）"""
    # 获取采购订单
    order = PurchaseOrder.query.get_or_404(order_id)

    # 检查订单状态
    if order.status != '准备入库':
        flash('只能为准备入库状态的采购订单创建入库单', 'warning')
        return redirect(url_for('purchase_order.view', id=order_id))

    # 检查用户权限
    if not current_user.can_access_area_by_id(order.area_id):
        flash('您没有权限为该区域创建入库单', 'danger')
        return redirect(url_for('purchase_order.index'))

    # 检查是否可以创建入库单
    if not order.can_create_stock_in():
        existing_stock_in = order.active_stock_in
        if existing_stock_in:
            flash('该采购订单已经创建了入库单，正在跳转到编辑页面', 'info')
            return redirect(url_for('stock_in.batch_editor_simplified', id=existing_stock_in.id))
        else:
            flash('该采购订单无法创建入库单', 'warning')
            return redirect(url_for('purchase_order.view', id=order_id))

    # 获取该区域的仓库
    warehouse = Warehouse.query.filter_by(area_id=order.area_id, status='正常').first()
    if not warehouse:
        flash('未找到可用的仓库，请先创建仓库', 'warning')
        return redirect(url_for('warehouse.create'))

    # 获取订单项目
    order_items = PurchaseOrderItem.query.filter_by(order_id=order_id).all()

    # 获取当前日期
    now = datetime.now()

    return render_template('stock_in/create_from_purchase_order.html',
                          title='从采购订单创建入库单',
                          order=order,
                          order_items=order_items,
                          warehouse=warehouse,
                          now=now)

@stock_in_bp.route('/stock-in/save-from-purchase-order', methods=['POST'])
@login_required
def save_from_purchase_order():
    """保存从采购订单创建的入库单"""
    data = request.get_json()

    if not data:
        return jsonify({
            'success': False,
            'message': '无效的请求数据'
        })

    try:
        # 获取基本信息
        order_id = data.get('order_id')
        warehouse_id = data.get('warehouse_id')
        stock_in_date = data.get('stock_in_date')
        notes = data.get('notes', '')
        items = data.get('items', [])

        # 验证数据
        if not order_id or not warehouse_id or not stock_in_date or not items:
            return jsonify({
                'success': False,
                'message': '缺少必要的数据'
            })

        # 获取采购订单
        order = PurchaseOrder.query.get_or_404(order_id)

        # 检查订单状态
        if order.status != '准备入库':
            return jsonify({
                'success': False,
                'message': '只能为准备入库状态的采购订单创建入库单'
            })

        # 检查用户权限
        if not current_user.can_access_area_by_id(order.area_id):
            return jsonify({
                'success': False,
                'message': '您没有权限为该区域创建入库单'
            })

        # 生成入库单号
        stock_in_number = f"RK{datetime.now().strftime('%Y%m%d%H%M%S')}"

        # 使用原始SQL语句创建入库单，避免ORM处理datetime字段
        sql = text("""
        INSERT INTO stock_ins (stock_in_number, warehouse_id, purchase_order_id, stock_in_date, stock_in_type, operator_id, status, notes)
        OUTPUT inserted.id
        VALUES (:stock_in_number, :warehouse_id, :purchase_order_id, :stock_in_date, :stock_in_type, :operator_id, :status, :notes)
        """)

        # 执行SQL语句
        result = db.session.execute(sql, {
            'stock_in_number': stock_in_number,
            'warehouse_id': warehouse_id,
            'purchase_order_id': order_id,
            'stock_in_date': stock_in_date,  # 直接使用字符串格式的日期
            'stock_in_type': '采购入库',
            'operator_id': current_user.id,
            'status': '待审核',
            'notes': f"从采购订单 {order.order_number} 创建\n{notes}"
        })

        # 获取新创建的入库单ID
        stock_in_id = result.fetchone()[0]

        # 获取创建的入库单对象
        stock_in = StockIn.query.get(stock_in_id)

        # 使用原始SQL查询默认存储位置，避免datetime字段问题
        storage_location_sql = text("""
        SELECT TOP 1 id, name FROM storage_locations
        WHERE warehouse_id = :warehouse_id AND status = :status
        """)

        storage_result = db.session.execute(storage_location_sql, {
            'warehouse_id': warehouse_id,
            'status': '正常'
        }).fetchone()

        if not storage_result:
            db.session.rollback()
            return jsonify({
                'success': False,
                'message': '未找到可用的存储位置，请先创建存储位置'
            })

        default_storage_location_id = storage_result[0]

        # 创建入库明细
        for item_data in items:
            # 获取采购订单明细
            purchase_item = PurchaseOrderItem.query.get(item_data.get('id'))
            if not purchase_item:
                continue

            # 生成批次号
            batch_number = f"B{datetime.now().strftime('%Y%m%d')}{uuid.uuid4().hex[:6]}"

            # 计算默认的生产日期和过期日期
            production_date = datetime.now().date()
            # 默认保质期为30天，如果食材有保质期信息则使用食材的保质期
            shelf_life = 30
            if purchase_item.ingredient and hasattr(purchase_item.ingredient, 'shelf_life') and purchase_item.ingredient.shelf_life:
                shelf_life = purchase_item.ingredient.shelf_life
            expiry_date = production_date + timedelta(days=shelf_life)

            # 使用原始SQL语句创建入库明细，避免ORM处理datetime字段
            insert_item_sql = text("""
            INSERT INTO stock_in_items (stock_in_id, ingredient_id, purchase_order_item_id, storage_location_id,
                                      batch_number, quantity, unit, unit_price, production_date, expiry_date,
                                      supplier_id, quality_status, notes)
            VALUES (:stock_in_id, :ingredient_id, :purchase_order_item_id, :storage_location_id,
                    :batch_number, :quantity, :unit, :unit_price, :production_date, :expiry_date,
                    :supplier_id, :quality_status, :notes)
            """)

            # 将日期对象转换为字符串格式
            production_date_str = production_date.strftime('%Y-%m-%d')
            expiry_date_str = expiry_date.strftime('%Y-%m-%d')

            db.session.execute(insert_item_sql, {
                'stock_in_id': stock_in.id,
                'ingredient_id': purchase_item.ingredient_id,
                'purchase_order_item_id': purchase_item.id,
                'storage_location_id': default_storage_location_id,
                'batch_number': batch_number,
                'quantity': item_data.get('actual_quantity'),
                'unit': item_data.get('unit'),
                'unit_price': item_data.get('unit_price'),
                'production_date': production_date_str,
                'expiry_date': expiry_date_str,
                'supplier_id': order.supplier_id,
                'quality_status': '良好',
                'notes': "从采购订单明细创建"
            })

        # 不再自动更新采购订单状态为已入库
        # 入库单确认后才会更新采购订单状态
        order.updated_at = datetime.now()

        # 提交事务
        db.session.commit()

        return jsonify({
            'success': True,
            'message': '入库单创建成功，请完善食材信息',
            'redirect_url': url_for('stock_in.batch_editor_simplified', id=stock_in.id)
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'创建入库单失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': f'创建入库单失败: {str(e)}'
        })

@stock_in_bp.route('/stock-in/import-from-purchase/<int:purchase_id>', methods=['GET'])
@login_required
def import_from_purchase(purchase_id):
    """从采购计划导入食材到入库单"""
    try:
        # 记录日志，帮助调试
        current_app.logger.info(f"用户 {current_user.username} 尝试从采购计划 {purchase_id} 导入食材")

        purchase_order = PurchaseOrder.query.get_or_404(purchase_id)
        current_app.logger.info(f"采购计划信息: ID={purchase_order.id}, 单号={purchase_order.order_number}, 状态={purchase_order.status}, 区域ID={purchase_order.area_id}")

        # 移除权限检查，允许任何登录用户从采购计划导入食材
        # 记录日志，但不进行权限限制
        current_app.logger.info(f"用户 {current_user.username} 正在从采购计划 {purchase_id} 导入食材，不进行权限检查")

        # 获取采购计划中的食材列表
        items = PurchaseOrderItem.query.filter_by(order_id=purchase_id).all()
        current_app.logger.info(f"采购计划 {purchase_id} 中有 {len(items)} 个食材明细")

        if not items:
            current_app.logger.warning(f"采购计划 {purchase_id} 没有食材明细")
            return jsonify({
                'success': False,
                'message': '该采购计划没有食材明细，请先添加食材明细或选择其他采购计划'
            })

        # 转换为JSON格式返回
        result = []
        for item in items:
            try:
                # 检查该食材是否需要检验检疫
                needs_inspection = False
                inspection_type = None
                if item.ingredient and item.ingredient.category_rel:
                    needs_inspection = item.ingredient.category_rel.needs_inspection
                    inspection_type = item.ingredient.category_rel.inspection_type

                # 计算默认的生产日期和过期日期
                production_date = datetime.now().date()
                # 默认保质期为30天，如果食材有保质期信息则使用食材的保质期
                shelf_life = 30
                if item.ingredient and hasattr(item.ingredient, 'shelf_life') and item.ingredient.shelf_life:
                    shelf_life = item.ingredient.shelf_life
                expiry_date = production_date + timedelta(days=shelf_life)

                # 记录每个食材的信息
                ingredient_name = item.ingredient.name if item.ingredient else f"未知食材(ID:{item.ingredient_id})"
                current_app.logger.info(f"食材明细: ID={item.id}, 名称={ingredient_name}, 数量={item.quantity}, 单位={item.unit}")

                # 获取分类名称
                category_name = '未分类'
                if item.ingredient and item.ingredient.category_rel:
                    category_name = item.ingredient.category_rel.name

                # 获取供应商信息
                supplier_id = None
                supplier_name = ''
                if purchase_order.supplier:
                    supplier_id = purchase_order.supplier_id
                    supplier_name = purchase_order.supplier.name

                # 获取单价
                unit_price = None
                if hasattr(item, 'unit_price') and item.unit_price:
                    try:
                        unit_price = float(item.unit_price)
                    except (ValueError, TypeError):
                        unit_price = 0.0

                # 生成批次号
                batch_number = f"B{datetime.now().strftime('%Y%m%d')}{uuid.uuid4().hex[:6]}"

                result.append({
                    'id': item.id,
                    'ingredient_id': item.ingredient_id,
                    'ingredient_name': ingredient_name,
                    'category': category_name,
                    'quantity': item.quantity,
                    'unit': item.unit,
                    'supplier_id': supplier_id,
                    'supplier_name': supplier_name,
                    'needs_inspection': needs_inspection,
                    'inspection_type': inspection_type,
                    'estimated_price': unit_price,
                    'production_date': production_date.strftime('%Y-%m-%d'),
                    'expiry_date': expiry_date.strftime('%Y-%m-%d'),
                    'batch_number': batch_number
                })
            except Exception as item_error:
                current_app.logger.error(f"处理食材明细时出错: {str(item_error)}")
                # 继续处理下一个食材，不中断整个过程

        current_app.logger.info(f"成功从采购计划 {purchase_id} 导入 {len(result)} 个食材明细")
        return jsonify({
            'success': True,
            'data': result
        })

    except Exception as e:
        current_app.logger.error(f"从采购计划导入食材时出错: {str(e)}")
        import traceback
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'success': False,
            'message': f'从采购计划导入食材时出错: {str(e)}'
        })

@stock_in_bp.route('/stock-in/<int:id>/add-items-batch', methods=['POST'])
@login_required
def add_items_batch(id):
    """批量添加入库明细"""
    stock_in = StockIn.query.get_or_404(id)

    # 不检查区域权限，只检查入库单状态
    # 记录日志，但不进行权限限制
    current_app.logger.info(f"用户 {current_user.username} 正在批量添加入库明细，不进行权限检查")

    # 只有待审核状态的入库单可以添加明细
    if stock_in.status != '待审核':
        return jsonify({
            'success': False,
            'message': '只有待审核状态的入库单可以添加明细'
        })

    # 获取批量提交的数据
    items_data = request.json.get('items', [])
    current_app.logger.info(f"收到批量添加请求，共 {len(items_data)} 个食材明细")

    if not items_data:
        return jsonify({
            'success': False,
            'message': '没有提供食材明细数据'
        })

    try:
        added_items = []
        for item_data in items_data:
            current_app.logger.info(f"处理食材明细: {item_data}")

            # 验证必要字段
            required_fields = ['ingredient_id', 'storage_location_id', 'batch_number', 'quantity', 'unit', 'production_date', 'expiry_date']
            for field in required_fields:
                if field not in item_data or not item_data[field]:
                    return jsonify({
                        'success': False,
                        'message': f'缺少必要字段: {field}'
                    })

            # 使用原始SQL语句创建入库明细
            sql = text("""
            INSERT INTO stock_in_items (stock_in_id, ingredient_id, storage_location_id, batch_number,
                                      quantity, unit, production_date, expiry_date, unit_price,
                                      supplier_id, quality_status, notes, created_at, updated_at)
            OUTPUT inserted.id
            VALUES (:stock_in_id, :ingredient_id, :storage_location_id, :batch_number,
                    :quantity, :unit, CONVERT(DATE, :production_date, 23),
                    CONVERT(DATE, :expiry_date, 23), :unit_price,
                    :supplier_id, :quality_status, :notes, GETDATE(), GETDATE())
            """)

            # 准备参数
            params = {
                'stock_in_id': id,
                'ingredient_id': item_data['ingredient_id'],
                'storage_location_id': item_data['storage_location_id'],
                'batch_number': item_data['batch_number'],
                'quantity': item_data['quantity'],
                'unit': item_data['unit'],
                'production_date': item_data['production_date'],
                'expiry_date': item_data['expiry_date'],
                'unit_price': item_data.get('unit_price'),
                'supplier_id': item_data.get('supplier_id'),
                'quality_status': item_data.get('quality_status', '良好'),
                'notes': item_data.get('notes', '')
            }

            current_app.logger.info(f"执行SQL插入，参数: {params}")

            # 执行SQL语句
            result = db.session.execute(sql, params)
            item_id = result.fetchone()[0]
            added_items.append(item_id)
            current_app.logger.info(f"成功添加食材明细，ID: {item_id}")

        # 提交事务
        db.session.commit()
        current_app.logger.info(f"批量添加入库明细成功，共添加 {len(added_items)} 个明细")
        return jsonify({
            'success': True,
            'message': f'批量添加入库明细成功，共添加 {len(added_items)} 个明细',
            'item_ids': added_items,
            'redirect_url': url_for('stock_in.batch_editor_simplified', id=id)
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"批量添加入库明细时出错: {str(e)}")
        import traceback
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'success': False,
            'message': f'批量添加入库明细时出错: {str(e)}'
        })

@stock_in_bp.route('/stock-in/<int:id>/upload-document', methods=['POST'])
@login_required
def upload_document(id):
    """上传入库单据"""
    stock_in = StockIn.query.get_or_404(id)

    # 不检查区域权限，只检查入库单状态
    # 记录日志，但不进行权限限制
    current_app.logger.info(f"用户 {current_user.username} 正在上传入库单据，不进行权限检查")

    # 只有待审核状态的入库单可以上传单据
    if stock_in.status != '待审核':
        return jsonify({
            'success': False,
            'message': '只有待审核状态的入库单可以上传单据'
        })

    # 检查是否有文件上传
    if 'document' not in request.files:
        return jsonify({
            'success': False,
            'message': '没有选择文件'
        })

    file = request.files['document']

    # 如果用户没有选择文件，浏览器也会提交一个空的文件
    if file.filename == '':
        return jsonify({
            'success': False,
            'message': '没有选择文件'
        })

    # 检查文件类型
    if file and '.' in file.filename and file.filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS:
        # 安全地获取文件名和扩展名
        original_filename = file.filename
        filename_parts = original_filename.rsplit('.', 1)
        name_part = secure_filename(filename_parts[0])
        ext_part = filename_parts[1].lower()

        # 确保文件名和扩展名都有效
        if not name_part:
            name_part = 'document'

        # 重新组合安全的文件名
        safe_filename = f"{name_part}.{ext_part}"

        # 创建存储目录
        document_dir = os.path.join(UPLOAD_FOLDER, f'stock_in_{id}')
        os.makedirs(document_dir, exist_ok=True)

        # 生成唯一文件名，保持扩展名
        unique_filename = f"{datetime.now().strftime('%Y%m%d%H%M%S')}_{safe_filename}"
        file_path = os.path.join(document_dir, unique_filename)

        # 保存文件
        file.save(file_path)

        # 获取文档类型
        document_type = request.form.get('document_type', '送货单')
        supplier_id = request.form.get('supplier_id', type=int)
        notes = request.form.get('notes', '')
        batch_numbers_text = request.form.get('batch_numbers', '')

        # 相对路径，用于存储到数据库（相对于static目录）
        # 确保使用正斜杠，适用于URL访问
        relative_path = f"uploads/stock_in_docs/stock_in_{id}/{unique_filename}"

        try:
            # 使用原始SQL语句创建文档记录
            sql = text("""
            INSERT INTO stock_in_documents (stock_in_id, document_type, file_path, supplier_id, notes, created_at)
            OUTPUT inserted.id
            VALUES (:stock_in_id, :document_type, :file_path, :supplier_id, :notes, GETDATE())
            """)

            # 执行SQL语句
            result = db.session.execute(sql, {
                'stock_in_id': id,
                'document_type': document_type,
                'file_path': relative_path,
                'supplier_id': supplier_id,
                'notes': notes
            })

            # 获取新创建的文档ID
            document_id = result.fetchone()[0]

            # 处理批次号关联
            associated_items = []
            if batch_numbers_text.strip():
                # 解析批次号
                batch_numbers = [line.strip() for line in batch_numbers_text.strip().split('\n') if line.strip()]

                if batch_numbers:
                    # 查找匹配的入库明细
                    for batch_number in batch_numbers:
                        # 查找匹配的入库明细
                        find_item_sql = text("""
                        SELECT id, ingredient_id FROM stock_in_items
                        WHERE stock_in_id = :stock_in_id AND batch_number = :batch_number
                        """)

                        item_result = db.session.execute(find_item_sql, {
                            'stock_in_id': id,
                            'batch_number': batch_number
                        }).fetchone()

                        if item_result:
                            item_id = item_result[0]
                            ingredient_id = item_result[1]

                            # 创建文档与入库明细的关联
                            associate_sql = text("""
                            INSERT INTO stock_in_document_items (document_id, item_id)
                            VALUES (:document_id, :item_id)
                            """)

                            db.session.execute(associate_sql, {
                                'document_id': document_id,
                                'item_id': item_id
                            })

                            # 获取食材名称
                            ingredient_sql = text("SELECT name FROM ingredients WHERE id = :id")
                            ingredient_name = db.session.execute(ingredient_sql, {'id': ingredient_id}).scalar()

                            associated_items.append({
                                'batch_number': batch_number,
                                'ingredient_name': ingredient_name
                            })

                    current_app.logger.info(f"文档 {document_id} 关联了 {len(associated_items)} 个批次")

            # 提交事务
            db.session.commit()

            # 构建响应消息
            message = '文档上传成功'
            if associated_items:
                batch_info = ', '.join([f"{item['ingredient_name']}({item['batch_number']})" for item in associated_items])
                message += f'，已自动关联批次：{batch_info}'

            return jsonify({
                'success': True,
                'message': message,
                'document': {
                    'id': document_id,
                    'document_type': document_type,
                    'file_name': safe_filename,
                    'file_path': relative_path
                },
                'associated_items': associated_items
            })

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"上传文档时出错: {str(e)}")
            return jsonify({
                'success': False,
                'message': f'上传文档时出错: {str(e)}'
            })

    return jsonify({
        'success': False,
        'message': '不支持的文件类型'
    })

@stock_in_bp.route('/stock-in/associate-document/<int:document_id>', methods=['POST'])
@login_required
def associate_document(document_id):
    """关联文档与入库明细"""
    # 获取文档信息
    try:
        # 使用原始SQL语句获取文档信息
        sql = text("""
        SELECT stock_in_id FROM stock_in_documents
        WHERE id = :id
        """)

        result = db.session.execute(sql, {'id': document_id}).fetchone()

        if not result:
            return jsonify({
                'success': False,
                'message': '文档不存在'
            })

        stock_in_id = result[0]

        # 不检查区域权限，只检查入库单状态
        stock_in = StockIn.query.get_or_404(stock_in_id)
        # 记录日志，但不进行权限限制
        current_app.logger.info(f"用户 {current_user.username} 正在关联文档与入库明细，不进行权限检查")

        # 获取要关联的入库明细ID列表
        item_ids = request.json.get('item_ids', [])

        if not item_ids:
            return jsonify({
                'success': False,
                'message': '请选择要关联的入库明细'
            })

        # 关联文档与入库明细
        for item_id in item_ids:
            # 验证入库明细是否属于当前入库单
            check_sql = text("""
            SELECT COUNT(*) FROM stock_in_items
            WHERE id = :id AND stock_in_id = :stock_in_id
            """)

            count = db.session.execute(check_sql, {
                'id': item_id,
                'stock_in_id': stock_in_id
            }).scalar()

            if count > 0:
                # 创建关联
                associate_sql = text("""
                INSERT INTO stock_in_document_items (document_id, item_id)
                VALUES (:document_id, :item_id)
                """)

                db.session.execute(associate_sql, {
                    'document_id': document_id,
                    'item_id': item_id
                })

        # 提交事务
        db.session.commit()

        return jsonify({
            'success': True,
            'message': '关联成功'
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"关联文档与入库明细时出错: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'关联文档与入库明细时出错: {str(e)}'
        })

@stock_in_bp.route('/stock-in/delete-document/<int:document_id>', methods=['POST'])
@login_required
def delete_document(document_id):
    """删除入库单据"""
    try:
        # 使用原始SQL语句获取文档信息
        sql = text("""
        SELECT stock_in_id FROM stock_in_documents
        WHERE id = :id
        """)

        result = db.session.execute(sql, {'id': document_id}).fetchone()

        if not result:
            return jsonify({
                'success': False,
                'message': '文档不存在'
            })

        stock_in_id = result[0]

        # 不检查区域权限，只检查入库单状态
        stock_in = StockIn.query.get_or_404(stock_in_id)
        # 记录日志，但不进行权限限制
        current_app.logger.info(f"用户 {current_user.username} 正在删除入库单据，不进行权限检查")

        # 只有待审核状态的入库单可以删除文档
        if stock_in.status != '待审核':
            return jsonify({
                'success': False,
                'message': '只有待审核状态的入库单可以删除文档'
            })

        # 获取文档文件路径
        file_path_sql = text("""
        SELECT file_path FROM stock_in_documents
        WHERE id = :id
        """)

        file_path_result = db.session.execute(file_path_sql, {'id': document_id}).fetchone()

        if file_path_result:
            file_path = file_path_result[0]
            # 构建完整的文件路径
            full_path = os.path.join(current_app.root_path, 'static', file_path)

            # 删除文件
            if os.path.exists(full_path):
                os.remove(full_path)

        # 删除文档与入库明细的关联
        delete_association_sql = text("""
        DELETE FROM stock_in_document_items
        WHERE document_id = :document_id
        """)

        db.session.execute(delete_association_sql, {'document_id': document_id})

        # 删除文档记录
        delete_document_sql = text("""
        DELETE FROM stock_in_documents
        WHERE id = :id
        """)

        db.session.execute(delete_document_sql, {'id': document_id})

        # 提交事务
        db.session.commit()

        return jsonify({
            'success': True,
            'message': '文档删除成功'
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"删除文档时出错: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'删除文档时出错: {str(e)}'
        })

@stock_in_bp.route('/stock-in/add-inspection/<int:item_id>', methods=['POST'])
@login_required
def add_inspection(item_id):
    """添加食材检验检疫记录"""
    # 获取入库明细
    stock_in_item = StockInItem.query.get_or_404(item_id)
    stock_in = StockIn.query.get_or_404(stock_in_item.stock_in_id)

    # 不检查区域权限，只检查入库单状态
    # 记录日志，但不进行权限限制
    current_app.logger.info(f"用户 {current_user.username} 正在添加食材检验检疫记录，不进行权限检查")

    # 只有待审核状态的入库单可以添加检验记录
    if stock_in.status != '待审核':
        return jsonify({
            'success': False,
            'message': '只有待审核状态的入库单可以添加检验记录'
        })

    # 获取表单数据
    data = request.json
    inspection_type = data.get('inspection_type')
    result = data.get('result')
    notes = data.get('notes', '')
    document_id = data.get('document_id')

    # 验证数据
    if not inspection_type or not result:
        return jsonify({
            'success': False,
            'message': '请填写检验类型和检验结果'
        })

    try:
        # 使用原始SQL语句创建检验记录
        sql = text("""
        INSERT INTO ingredient_inspections (stock_in_item_id, inspector_id, inspection_date,
                                          inspection_type, result, notes, document_id, created_at)
        OUTPUT inserted.id
        VALUES (:stock_in_item_id, :inspector_id, GETDATE(),
                :inspection_type, :result, :notes, :document_id, GETDATE())
        """)

        # 执行SQL语句
        result_query = db.session.execute(sql, {
            'stock_in_item_id': item_id,
            'inspector_id': current_user.id,
            'inspection_type': inspection_type,
            'result': result,
            'notes': notes,
            'document_id': document_id
        })

        # 获取新创建的检验记录ID
        inspection_id = result_query.fetchone()[0]

        # 更新入库明细的质量检查结果
        update_sql = text("""
        UPDATE stock_in_items
        SET quality_check_result = :result, quality_check_notes = :notes
        WHERE id = :id
        """)

        db.session.execute(update_sql, {
            'result': result,
            'notes': notes,
            'id': item_id
        })

        # 提交事务
        db.session.commit()

        return jsonify({
            'success': True,
            'message': '检验记录添加成功',
            'inspection_id': inspection_id
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"添加检验记录时出错: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'添加检验记录时出错: {str(e)}'
        })

@stock_in_bp.route('/stock-in/get-inspections/<int:item_id>', methods=['GET'])
@login_required
def get_inspections(item_id):
    """获取食材检验检疫记录"""
    # 获取入库明细
    stock_in_item = StockInItem.query.get_or_404(item_id)
    stock_in = StockIn.query.get_or_404(stock_in_item.stock_in_id)

    # 不检查区域权限
    # 记录日志，但不进行权限限制
    current_app.logger.info(f"用户 {current_user.username} 正在获取食材检验检疫记录，不进行权限检查")

    # 获取检验记录
    inspections = IngredientInspection.query.filter_by(stock_in_item_id=item_id).all()

    # 转换为JSON格式返回
    result = []
    for inspection in inspections:
        result.append({
            'id': inspection.id,
            'inspector_name': inspection.inspector.real_name or inspection.inspector.username,
            'inspection_date': inspection.inspection_date.strftime('%Y-%m-%d') if inspection.inspection_date else '',
            'inspection_type': inspection.inspection_type,
            'result': inspection.result,
            'notes': inspection.notes,
            'document_id': inspection.document_id,
            'document_type': inspection.document.document_type if inspection.document else None,
            'document_path': inspection.document.file_path if inspection.document else None,
            'created_at': inspection.created_at.strftime('%Y-%m-%d %H:%M:%S') if inspection.created_at else ''
        })

    return jsonify({
        'success': True,
        'data': result
    })

@stock_in_bp.route('/stock-in/ingredient-trace/<batch_number>', methods=['GET'])
@login_required
def ingredient_trace(batch_number):
    """食材质量追溯"""
    # 查询该批次的入库记录
    stock_in_items = StockInItem.query.filter_by(batch_number=batch_number).all()

    if not stock_in_items:
        flash('未找到该批次的食材记录', 'warning')
        return redirect(url_for('stock_in.index'))

    # 不检查区域权限
    # 记录日志，但不进行权限限制
    current_app.logger.info(f"用户 {current_user.username} 正在查看批次 {batch_number} 的食材质量追溯，不进行权限检查")

    # 获取相关的采购记录
    purchase_records = []
    for item in stock_in_items:
        if item.purchase_order_item:
            purchase_records.append(item.purchase_order_item)

    # 获取相关的检验记录
    inspection_records = []
    for item in stock_in_items:
        inspections = IngredientInspection.query.filter_by(stock_in_item_id=item.id).all()
        inspection_records.extend(inspections)

    # 获取相关的库存记录
    inventory_records = Inventory.query.filter_by(batch_number=batch_number).all()

    return render_template('stock_in/trace.html',
                          batch_number=batch_number,
                          stock_in_items=stock_in_items,
                          purchase_records=purchase_records,
                          inspection_records=inspection_records,
                          inventory_records=inventory_records)

@stock_in_bp.route('/stock-in/confirm/<int:id>', methods=['GET', 'POST'])
@login_required
def confirm(id):
    """确认入库单"""
    stock_in = StockIn.query.get_or_404(id)

    # 检查用户是否有权限操作
    if not current_user.can_access_area_by_id(stock_in.warehouse.area_id):
        flash('您没有权限操作该入库单', 'danger')
        return redirect(url_for('stock_in.index'))

    # 只有待审核状态的入库单可以确认
    if stock_in.status != '待审核':
        flash('只有待审核状态的入库单可以确认', 'warning')
        return redirect(url_for('stock_in.view', id=id))

    if request.method == 'POST':
        # 获取表单数据
        item_data = {}
        for key, value in request.form.items():
            if key.startswith('quantity_'):
                item_id = int(key.replace('quantity_', ''))
                if item_id not in item_data:
                    item_data[item_id] = {}
                item_data[item_id]['quantity'] = float(value)
            elif key.startswith('quality_'):
                item_id = int(key.replace('quality_', ''))
                if item_id not in item_data:
                    item_data[item_id] = {}
                item_data[item_id]['quality_status'] = value
            elif key.startswith('notes_'):
                item_id = int(key.replace('notes_', ''))
                if item_id not in item_data:
                    item_data[item_id] = {}
                item_data[item_id]['notes'] = value

        try:
            # 更新入库明细
            for item_id, data in item_data.items():
                # 验证入库明细是否属于当前入库单
                check_sql = text("""
                SELECT COUNT(*) FROM stock_in_items
                WHERE id = :id AND stock_in_id = :stock_in_id
                """)

                result = db.session.execute(check_sql, {
                    'id': item_id,
                    'stock_in_id': id
                }).scalar()

                if result > 0:
                    # 构建更新字段
                    update_fields = []
                    params = {'id': item_id}

                    if 'quantity' in data:
                        update_fields.append("quantity = :quantity")
                        params['quantity'] = data['quantity']

                    if 'quality_status' in data:
                        update_fields.append("quality_status = :quality_status")
                        params['quality_status'] = data['quality_status']

                    if 'notes' in data:
                        update_fields.append("notes = :notes")
                        params['notes'] = data['notes']

                    if update_fields:
                        # 使用原始SQL语句更新入库明细
                        update_sql = text(f"""
                        UPDATE stock_in_items
                        SET {', '.join(update_fields)}
                        WHERE id = :id
                        """)

                        db.session.execute(update_sql, params)

            # 使用原始SQL语句更新入库单状态
            update_stock_in_sql = text("""
            UPDATE stock_ins
            SET status = :status, inspector_id = :inspector_id
            WHERE id = :id
            """)

            db.session.execute(update_stock_in_sql, {
                'status': '已审核',
                'inspector_id': current_user.id,
                'id': id
            })

            # 提交事务
            db.session.commit()

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"确认入库单时出错: {str(e)}")
            flash(f'确认入库单时出错: {str(e)}', 'danger')
            return redirect(url_for('stock_in.confirm', id=id))

        flash('入库单确认成功', 'success')
        return redirect(url_for('stock_in.batch_editor_simplified', id=id))

    # GET请求，显示确认表单
    # 获取入库明细
    stock_in_items = StockInItem.query.filter_by(stock_in_id=id).all()

    return render_template('stock_in/confirm.html',
                          stock_in=stock_in,
                          stock_in_items=stock_in_items,
                          title='确认入库单')

@stock_in_bp.route('/stock-in/by-ingredient/<string:ingredient_code>')
@login_required
def by_ingredient_code(ingredient_code):
    """
    按食材编码查询入库记录列表
    显示某个食材的所有入库明细。
    """
    # First, find the ingredient by code
    ingredient = Ingredient.query.filter_by(code=ingredient_code).first()

    # If ingredient not found by code, return 404
    if not ingredient:
        abort(404, description="Ingredient not found with provided code")

    # Check user permission based on accessible areas for associated stock in orders
    accessible_area_ids = get_accessible_area_ids()

    # Query for StockInItems related to the ingredient's ID, joining with StockIn and filtering by accessible areas
    stock_in_items = StockInItem.query.options(
        joinedload(StockInItem.stock_in).joinedload(StockIn.supplier),
        joinedload(StockInItem.ingredient) # Eager load ingredient for display
    ).join(StockIn).join(StockIn.warehouse).filter(
        StockInItem.ingredient_id == ingredient.id, # Use the found ingredient's ID
        StockIn.warehouse.has(Warehouse.area_id.in_(accessible_area_ids)) # Check if the stock in's warehouse is in an accessible area
    ).order_by(StockIn.stock_in_date.desc(), StockInItem.created_at.desc()).all()

    return render_template(
        'stock_in/by_ingredient.html',
        ingredient=ingredient,
        stock_in_items=stock_in_items
    )

# 标签打印相关路由
@stock_in_bp.route('/stock-in/<int:stock_in_id>/labels/print')
@csrf.exempt
@login_required
def print_all_labels(stock_in_id):
    """批量打印入库单所有食材标签"""
    stock_in = StockIn.query.get_or_404(stock_in_id)

    # 检查用户是否有权限查看
    if not current_user.can_access_area_by_id(stock_in.warehouse.area_id):
        flash('您没有权限查看该入库单', 'danger')
        return redirect(url_for('stock_in.index'))

    # 获取入库明细
    stock_in_items = StockInItem.query.filter_by(stock_in_id=stock_in_id).all()

    if not stock_in_items:
        flash('该入库单没有明细，无法打印标签', 'warning')
        return redirect(url_for('stock_in.view_details', id=stock_in_id))

    # 为每个食材生成二维码
    items_with_qr = []
    for item in stock_in_items:
        # 生成溯源二维码数据
        qr_data = {
            'type': 'ingredient_trace',
            'batch_number': item.batch_number,
            'ingredient_id': item.ingredient_id,
            'ingredient_name': item.ingredient.name,
            'stock_in_id': stock_in_id,
            'stock_in_date': stock_in.stock_in_date.strftime('%Y-%m-%d') if stock_in.stock_in_date else '',
            'supplier': item.supplier.name if item.supplier else '',
            'production_date': item.production_date.strftime('%Y-%m-%d') if item.production_date else '',
            'expiry_date': item.expiry_date.strftime('%Y-%m-%d') if item.expiry_date else ''
        }

        # 生成二维码
        qr_code_base64 = generate_qr_code(json.dumps(qr_data, ensure_ascii=False))

        # 添加二维码到食材信息
        item.qr_code = qr_code_base64
        items_with_qr.append(item)

    return render_template('stock_in/labels_print.html', items=items_with_qr)

@stock_in_bp.route('/stock-in/labels/print')
@csrf.exempt
@login_required
def print_selected_labels():
    """打印选中的食材标签"""
    item_ids = request.args.get('items', '')

    if not item_ids:
        flash('请选择要打印标签的食材', 'warning')
        return redirect(url_for('stock_in.index'))

    try:
        item_id_list = [int(id.strip()) for id in item_ids.split(',') if id.strip()]
    except ValueError:
        flash('无效的食材ID', 'danger')
        return redirect(url_for('stock_in.index'))

    # 获取选中的入库明细
    stock_in_items = StockInItem.query.filter(StockInItem.id.in_(item_id_list)).all()

    if not stock_in_items:
        flash('未找到选中的食材', 'warning')
        return redirect(url_for('stock_in.index'))

    # 检查用户权限
    for item in stock_in_items:
        if not current_user.can_access_area_by_id(item.stock_in.warehouse.area_id):
            flash('您没有权限打印某些食材的标签', 'danger')
            return redirect(url_for('stock_in.index'))

    # 为每个食材生成二维码
    items_with_qr = []
    for item in stock_in_items:
        # 生成溯源二维码数据
        qr_data = {
            'type': 'ingredient_trace',
            'batch_number': item.batch_number,
            'ingredient_id': item.ingredient_id,
            'ingredient_name': item.ingredient.name,
            'stock_in_id': item.stock_in_id,
            'stock_in_date': item.stock_in.stock_in_date.strftime('%Y-%m-%d') if item.stock_in.stock_in_date else '',
            'supplier': item.supplier.name if item.supplier else '',
            'production_date': item.production_date.strftime('%Y-%m-%d') if item.production_date else '',
            'expiry_date': item.expiry_date.strftime('%Y-%m-%d') if item.expiry_date else ''
        }

        # 生成二维码
        qr_code_base64 = generate_qr_code(json.dumps(qr_data, ensure_ascii=False))

        # 添加二维码到食材信息
        item.qr_code = qr_code_base64
        items_with_qr.append(item)

    return render_template('stock_in/labels_print.html', items=items_with_qr)

@stock_in_bp.route('/stock-in/item/<int:item_id>/label/print')
@csrf.exempt
@login_required
def print_single_label(item_id):
    """打印单个食材标签"""
    stock_in_item = StockInItem.query.get_or_404(item_id)

    # 检查用户是否有权限查看
    if not current_user.can_access_area_by_id(stock_in_item.stock_in.warehouse.area_id):
        flash('您没有权限打印该食材标签', 'danger')
        return redirect(url_for('stock_in.index'))

    # 生成溯源二维码数据
    qr_data = {
        'type': 'ingredient_trace',
        'batch_number': stock_in_item.batch_number,
        'ingredient_id': stock_in_item.ingredient_id,
        'ingredient_name': stock_in_item.ingredient.name,
        'stock_in_id': stock_in_item.stock_in_id,
        'stock_in_date': stock_in_item.stock_in.stock_in_date.strftime('%Y-%m-%d') if stock_in_item.stock_in.stock_in_date else '',
        'supplier': stock_in_item.supplier.name if stock_in_item.supplier else '',
        'production_date': stock_in_item.production_date.strftime('%Y-%m-%d') if stock_in_item.production_date else '',
        'expiry_date': stock_in_item.expiry_date.strftime('%Y-%m-%d') if stock_in_item.expiry_date else ''
    }

    # 生成二维码
    qr_code_base64 = generate_qr_code(json.dumps(qr_data, ensure_ascii=False))

    # 添加二维码到食材信息
    stock_in_item.qr_code = qr_code_base64

    return render_template('stock_in/labels_print.html', items=[stock_in_item])

def generate_qr_code(data):
    """生成二维码并返回base64编码的图片"""
    try:
        import qrcode
        from io import BytesIO
        import base64

        # 创建二维码实例
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_L,
            box_size=10,
            border=4,
        )

        # 添加数据
        qr.add_data(data)
        qr.make(fit=True)

        # 创建图片
        img = qr.make_image(fill_color="black", back_color="white")

        # 转换为base64
        buffer = BytesIO()
        img.save(buffer, format='PNG')
        buffer.seek(0)

        return base64.b64encode(buffer.getvalue()).decode()

    except ImportError:
        current_app.logger.warning("qrcode库未安装，无法生成二维码")
        return None
    except Exception as e:
        current_app.logger.error(f"生成二维码时出错: {str(e)}")
        return None


