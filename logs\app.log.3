2025-06-03 13:22:00,995 INFO: 批次编辑器 - 入库单ID: 93 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_in.py:799]
2025-06-03 13:22:01,004 INFO: 项目ID: 207, 食材: 米饭, 批次号: B20250603b9b7cb, 单价: 0.0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_in.py:801]
2025-06-03 13:22:30,372 INFO: 批次编辑器保存 - 入库单ID: 93 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_in.py:917]
2025-06-03 13:22:30,372 INFO: 选中的项目: ['207'] [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_in.py:918]
2025-06-03 13:22:30,373 INFO: 单价字段 - unit_price_207: 68 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_in.py:921]
2025-06-03 13:22:30,378 INFO: 项目 207 详细信息: [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_in.py:948]
2025-06-03 13:22:30,378 INFO:   - 供应商ID: 28 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_in.py:949]
2025-06-03 13:22:30,378 INFO:   - 存储位置ID: 9 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_in.py:950]
2025-06-03 13:22:30,379 INFO:   - 数量: 1000.0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_in.py:951]
2025-06-03 13:22:30,379 INFO:   - 单价: 68 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_in.py:952]
2025-06-03 13:22:30,379 INFO:   - 生产日期: 2025-06-03 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_in.py:953]
2025-06-03 13:22:30,380 INFO:   - 过期日期: 2025-07-03 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_in.py:954]
2025-06-03 13:29:47,391 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:786]
2025-06-03 13:32:09,804 INFO: 当前用户: 18373062333 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:94]
2025-06-03 13:32:09,805 INFO: 用户区域ID: 42 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:95]
2025-06-03 13:32:09,807 INFO: 用户区域名称: 朝阳区实验中学 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:96]
2025-06-03 13:32:09,807 INFO: 是否管理员: 0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:97]
2025-06-03 13:32:15,582 INFO: 收到食谱分析请求: {'area_id': 42, 'consumption_date': '2025-06-03', 'meal_types': ['早餐']} [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:338]
2025-06-03 13:32:15,584 INFO: 查询日期: 2025-06-03, 区域: 42, 餐次: ['早餐'] [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:356]
2025-06-03 13:32:15,586 INFO: 查询餐次: 早餐 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:361]
2025-06-03 13:32:15,590 INFO: 查询结果: 找到 0 个菜单计划 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:370]
2025-06-03 13:32:15,592 INFO: 该区域所有菜单计划数量: 0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:374]
2025-06-03 13:32:15,592 INFO: 未找到日菜单，查询周菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:430]
2025-06-03 13:32:15,592 INFO: 星期几: 2 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:436]
2025-06-03 13:32:15,597 INFO: 找到 1 个周菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:445]
2025-06-03 13:32:15,602 INFO: 周菜单 37 中找到 6 个食谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:456]
2025-06-03 13:32:15,607 INFO: 分析食谱: 🏫 鲜椒青瓜干, recipe_id=371 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:464]
2025-06-03 13:32:15,613 INFO: 找到 6 个食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:471]
2025-06-03 13:32:15,629 INFO: 分析食谱: 🏫 黄米南瓜盅, recipe_id=368 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:464]
2025-06-03 13:32:15,631 INFO: 找到 6 个食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:471]
2025-06-03 13:32:15,640 INFO: 分析食谱: 🏫 黑木耳炒山药, recipe_id=367 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:464]
2025-06-03 13:32:15,641 INFO: 找到 7 个食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:471]
2025-06-03 13:32:15,652 INFO: 分析食谱: 🏫 鲜笋烧仔排, recipe_id=370 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:464]
2025-06-03 13:32:15,653 INFO: 找到 6 个食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:471]
2025-06-03 13:32:15,662 INFO: 分析食谱: 🏫 鲜蚕豆烧大雁, recipe_id=369 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:464]
2025-06-03 13:32:15,663 INFO: 找到 5 个食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:471]
2025-06-03 13:32:15,670 INFO: 分析食谱: 🏫 缤纷吐司蒸, recipe_id=153 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:464]
2025-06-03 13:32:15,673 INFO: 找到 4 个食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:471]
2025-06-03 13:32:17,433 INFO: 收到食谱分析请求: {'area_id': 42, 'consumption_date': '2025-06-03', 'meal_types': ['早餐', '午餐']} [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:338]
2025-06-03 13:32:17,435 INFO: 查询日期: 2025-06-03, 区域: 42, 餐次: ['早餐', '午餐'] [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:356]
2025-06-03 13:32:17,435 INFO: 查询餐次: 早餐 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:361]
2025-06-03 13:32:17,436 INFO: 查询结果: 找到 0 个菜单计划 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:370]
2025-06-03 13:32:17,437 INFO: 该区域所有菜单计划数量: 0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:374]
2025-06-03 13:32:17,437 INFO: 未找到日菜单，查询周菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:430]
2025-06-03 13:32:17,438 INFO: 星期几: 2 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:436]
2025-06-03 13:32:17,439 INFO: 找到 1 个周菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:445]
2025-06-03 13:32:17,440 INFO: 周菜单 37 中找到 6 个食谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:456]
2025-06-03 13:32:17,441 INFO: 分析食谱: 🏫 鲜椒青瓜干, recipe_id=371 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:464]
2025-06-03 13:32:17,442 INFO: 找到 6 个食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:471]
2025-06-03 13:32:17,452 INFO: 分析食谱: 🏫 黄米南瓜盅, recipe_id=368 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:464]
2025-06-03 13:32:17,453 INFO: 找到 6 个食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:471]
2025-06-03 13:32:17,461 INFO: 分析食谱: 🏫 黑木耳炒山药, recipe_id=367 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:464]
2025-06-03 13:32:17,462 INFO: 找到 7 个食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:471]
2025-06-03 13:32:17,473 INFO: 分析食谱: 🏫 鲜笋烧仔排, recipe_id=370 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:464]
2025-06-03 13:32:17,474 INFO: 找到 6 个食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:471]
2025-06-03 13:32:17,482 INFO: 分析食谱: 🏫 鲜蚕豆烧大雁, recipe_id=369 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:464]
2025-06-03 13:32:17,483 INFO: 找到 5 个食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:471]
2025-06-03 13:32:17,490 INFO: 分析食谱: 🏫 缤纷吐司蒸, recipe_id=153 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:464]
2025-06-03 13:32:17,491 INFO: 找到 4 个食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:471]
