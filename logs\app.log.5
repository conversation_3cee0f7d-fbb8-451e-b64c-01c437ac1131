2025-06-03 13:32:18,262 INFO: 找到 5 个食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:471]
2025-06-03 13:32:18,273 INFO: 分析食谱: 🏫 西瓜桃子面, recipe_id=152 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:464]
2025-06-03 13:32:18,274 INFO: 找到 8 个食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:471]
2025-06-03 13:32:18,285 INFO: 分析食谱: 🏫 黄米南瓜盅, recipe_id=368 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:464]
2025-06-03 13:32:18,286 INFO: 找到 6 个食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:471]
2025-06-03 13:32:18,295 INFO: 查询餐次: 晚餐 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:361]
2025-06-03 13:32:18,296 INFO: 查询结果: 找到 0 个菜单计划 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:370]
2025-06-03 13:32:18,297 INFO: 该区域所有菜单计划数量: 0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:374]
2025-06-03 13:32:18,297 INFO: 未找到日菜单，查询周菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:430]
2025-06-03 13:32:18,297 INFO: 星期几: 2 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:436]
2025-06-03 13:32:18,299 INFO: 找到 1 个周菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:445]
2025-06-03 13:32:18,299 INFO: 周菜单 37 中找到 5 个食谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:456]
2025-06-03 13:32:18,300 INFO: 分析食谱: 🏫 鲜笋烧仔排, recipe_id=370 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:464]
2025-06-03 13:32:18,302 INFO: 找到 6 个食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:471]
2025-06-03 13:32:18,316 INFO: 分析食谱: 🏫 米饭, recipe_id=154 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:464]
2025-06-03 13:32:18,319 INFO: 找到 1 个食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:471]
2025-06-03 13:32:18,321 INFO: 分析食谱: 🏫 西瓜桃子面, recipe_id=152 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:464]
2025-06-03 13:32:18,322 INFO: 找到 8 个食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:471]
2025-06-03 13:32:18,333 INFO: 分析食谱: 🏫 鲜蚕豆烧大雁, recipe_id=369 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:464]
2025-06-03 13:32:18,334 INFO: 找到 5 个食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:471]
2025-06-03 13:32:18,341 INFO: 分析食谱: 🏫 缤纷吐司蒸, recipe_id=153 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:464]
2025-06-03 13:32:18,342 INFO: 找到 4 个食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:471]
2025-06-03 13:32:35,884 INFO: 超级编辑器接收到的数据: [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:202]
2025-06-03 13:32:35,884 INFO:   area_id: 42 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:203]
2025-06-03 13:32:35,886 INFO:   warehouse_id: 4 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:204]
2025-06-03 13:32:35,887 INFO:   consumption_date: 2025-06-03 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:205]
2025-06-03 13:32:35,887 INFO:   meal_types: ['早餐', '午餐', '晚餐'] [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:206]
2025-06-03 13:32:35,887 INFO:   diners_count: 1 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:207]
2025-06-03 13:32:35,888 INFO:   notes:  [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:208]
2025-06-03 13:32:35,889 INFO: 创建包含餐次 早餐+午餐+晚餐 的消耗计划 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:235]
2025-06-03 13:32:35,893 INFO: 未找到对应的菜单计划，将创建独立的消耗计划: area_id=42, date=2025-06-03, meal_types=['早餐', '午餐', '晚餐'] [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:252]
2025-06-03 13:32:35,897 INFO: 创建了消耗计划 ID: 35 (餐次: 早餐+午餐+晚餐) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:277]
2025-06-03 13:32:35,992 INFO: 当前用户: 18373062333 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:94]
2025-06-03 13:32:35,992 INFO: 用户区域ID: 42 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:95]
2025-06-03 13:32:35,994 INFO: 用户区域名称: 朝阳区实验中学 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:96]
2025-06-03 13:32:35,994 INFO: 是否管理员: 0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:97]
2025-06-03 13:32:43,420 INFO: 当前用户: 18373062333 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:94]
2025-06-03 13:32:43,420 INFO: 用户区域ID: 42 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:95]
2025-06-03 13:32:43,422 INFO: 用户区域名称: 朝阳区实验中学 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:96]
2025-06-03 13:32:43,422 INFO: 是否管理员: 0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:97]
2025-06-03 13:32:50,084 INFO: 当前用户: 18373062333 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:94]
2025-06-03 13:32:50,085 INFO: 用户区域ID: 42 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:95]
2025-06-03 13:32:50,087 INFO: 用户区域名称: 朝阳区实验中学 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:96]
2025-06-03 13:32:50,087 INFO: 是否管理员: 0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:97]
2025-06-03 13:32:57,603 INFO: 使用库存记录: ID=63, 批次号=B202506029f5974, 数量=3000.00 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:895]
2025-06-03 13:32:57,603 INFO: 准备更新库存: ID=63, 减少数量=3000.0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:916]
2025-06-03 13:32:57,604 INFO: 使用库存记录: ID=64, 批次号=B202506027a8ce4, 数量=210.00 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:895]
2025-06-03 13:32:57,605 INFO: 准备更新库存: ID=64, 减少数量=210.0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:916]
2025-06-03 13:32:57,605 INFO: 使用库存记录: ID=65, 批次号=B20250602181662, 数量=377.00 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:895]
2025-06-03 13:32:57,605 INFO: 准备更新库存: ID=65, 减少数量=377.0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:916]
2025-06-03 13:32:57,606 INFO: 使用库存记录: ID=66, 批次号=B20250602854a46, 数量=199.90 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:895]
2025-06-03 13:32:57,606 INFO: 准备更新库存: ID=66, 减少数量=199.9 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:916]
2025-06-03 13:32:57,606 INFO: 使用库存记录: ID=67, 批次号=B2025060290b52e, 数量=400.00 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:895]
2025-06-03 13:32:57,607 INFO: 准备更新库存: ID=67, 减少数量=400.0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:916]
2025-06-03 13:32:57,607 INFO: 使用库存记录: ID=68, 批次号=B202506027f87f8, 数量=400.00 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:895]
2025-06-03 13:32:57,607 INFO: 准备更新库存: ID=68, 减少数量=400.0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:916]
2025-06-03 13:32:57,608 INFO: 使用库存记录: ID=69, 批次号=B202506025e4d48, 数量=210.00 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:895]
2025-06-03 13:32:57,608 INFO: 准备更新库存: ID=69, 减少数量=210.0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:916]
