2025-06-03 12:52:49,976 INFO: 项目ID: 207, 食材: 米饭, 单价: 0.0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_in.py:801]
2025-06-03 12:56:32,818 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-03 12:56:32,832 INFO: 成功获取 0 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-03 12:56:37,945 INFO: 当前用户: 18373062333 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:94]
2025-06-03 12:56:37,945 INFO: 用户区域ID: 42 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:95]
2025-06-03 12:56:37,949 INFO: 用户区域名称: 朝阳区实验中学 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:96]
2025-06-03 12:56:37,950 INFO: 是否管理员: 0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:97]
2025-06-03 12:57:07,882 INFO: 当前用户: 18373062333 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:94]
2025-06-03 12:57:07,882 INFO: 用户区域ID: 42 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:95]
2025-06-03 12:57:07,886 INFO: 用户区域名称: 朝阳区实验中学 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:96]
2025-06-03 12:57:07,886 INFO: 是否管理员: 0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:97]
2025-06-03 12:57:21,638 INFO: 当前用户: 18373062333 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:94]
2025-06-03 12:57:21,639 INFO: 用户区域ID: 42 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:95]
2025-06-03 12:57:21,644 INFO: 用户区域名称: 朝阳区实验中学 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:96]
2025-06-03 12:57:21,644 INFO: 是否管理员: 0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:97]
2025-06-03 13:03:20,913 INFO: 批次编辑器 - 入库单ID: 93 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_in.py:799]
2025-06-03 13:03:20,922 INFO: 项目ID: 207, 食材: 米饭, 单价: 0.0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_in.py:801]
2025-06-03 13:03:22,376 INFO: 批次编辑器 - 入库单ID: 93 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_in.py:799]
2025-06-03 13:03:22,382 INFO: 项目ID: 207, 食材: 米饭, 单价: 0.0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_in.py:801]
2025-06-03 13:03:24,605 INFO: 批次编辑器 - 入库单ID: 93 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_in.py:799]
2025-06-03 13:03:24,614 INFO: 项目ID: 207, 食材: 米饭, 单价: 0.0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_in.py:801]
2025-06-03 13:03:51,001 INFO: 用户 18373062333 正在上传入库单据，不进行权限检查 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_in.py:2205]
2025-06-03 13:03:51,097 INFO: 文档 18 关联了 1 个批次 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_in.py:2330]
2025-06-03 13:03:52,934 INFO: 批次编辑器 - 入库单ID: 93 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_in.py:799]
2025-06-03 13:03:52,940 INFO: 项目ID: 207, 食材: 米饭, 单价: 0.0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_in.py:801]
2025-06-03 13:09:18,087 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:786]
2025-06-03 13:09:23,287 INFO: 批次编辑器 - 入库单ID: 93 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_in.py:799]
2025-06-03 13:09:23,302 INFO: 项目ID: 207, 食材: 米饭, 批次号: B20250603b9b7cb, 单价: 0.0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_in.py:801]
2025-06-03 13:09:55,572 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-03 13:09:55,601 INFO: 成功获取 0 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-03 13:10:03,884 INFO: 批次编辑器 - 入库单ID: 93 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_in.py:799]
2025-06-03 13:10:03,890 INFO: 项目ID: 207, 食材: 米饭, 批次号: B20250603b9b7cb, 单价: 0.0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_in.py:801]
2025-06-03 13:11:12,651 INFO: 批次编辑器 - 入库单ID: 93 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_in.py:799]
2025-06-03 13:11:12,657 INFO: 项目ID: 207, 食材: 米饭, 批次号: B20250603b9b7cb, 单价: 0.0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_in.py:801]
2025-06-03 13:14:19,873 INFO: 使用消耗计划的区域ID: 42 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:217]
2025-06-03 13:14:19,874 INFO: 通过消耗计划信息读取菜谱：日期=2024-12-20, 餐次=晚餐, 区域ID=42 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:227]
2025-06-03 13:14:19,874 INFO: 查询周菜单：日期=2024-12-20, 星期=4(0=周一), day_of_week=5, 餐次=晚餐, 区域ID=42 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:242]
2025-06-03 13:14:19,882 INFO: 找到 0 个周菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:252]
2025-06-03 13:14:19,892 INFO: 未找到匹配的周菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:277]
2025-06-03 13:14:19,892 INFO: 最终获取到 0 个食谱用于关联显示 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:280]
2025-06-03 13:14:19,895 INFO: 步骤1: 读取消耗日期: 2024-12-20, 餐次: 晚餐 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:27]
2025-06-03 13:14:19,895 INFO: 步骤1完成: 找到 0 个菜谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:73]
2025-06-03 13:14:19,901 INFO: 步骤2: 为出库食材 '五花肉' 查找对应菜谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:80]
2025-06-03 13:14:19,904 INFO: 步骤2: 为出库食材 '猪肉' 查找对应菜谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:80]
2025-06-03 13:14:19,905 INFO: 步骤2: 为出库食材 '牛肉' 查找对应菜谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:80]
2025-06-03 13:14:19,905 INFO: 溯源完成: 找到 0 条溯源记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:137]
2025-06-03 13:14:19,917 ERROR: 获取溯源信息失败: 'StockIn' object has no attribute 'supplier' [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:396]
2025-06-03 13:14:19,919 ERROR: 获取溯源信息失败: 'StockIn' object has no attribute 'supplier' [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:396]
2025-06-03 13:14:19,920 ERROR: 获取溯源信息失败: 'StockIn' object has no attribute 'supplier' [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:396]
2025-06-03 13:15:04,151 INFO: 批次编辑器 - 入库单ID: 93 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_in.py:799]
2025-06-03 13:15:04,158 INFO: 项目ID: 207, 食材: 米饭, 批次号: B20250603b9b7cb, 单价: 0.0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_in.py:801]
2025-06-03 13:15:29,969 INFO: 批次编辑器 - 入库单ID: 93 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_in.py:799]
2025-06-03 13:15:29,975 INFO: 项目ID: 207, 食材: 米饭, 批次号: B20250603b9b7cb, 单价: 0.0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_in.py:801]
2025-06-03 13:15:31,249 INFO: 批次编辑器 - 入库单ID: 93 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_in.py:799]
2025-06-03 13:15:31,252 INFO: 项目ID: 207, 食材: 米饭, 批次号: B20250603b9b7cb, 单价: 0.0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_in.py:801]
2025-06-03 13:15:32,342 INFO: 批次编辑器 - 入库单ID: 93 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_in.py:799]
2025-06-03 13:15:32,348 INFO: 项目ID: 207, 食材: 米饭, 批次号: B20250603b9b7cb, 单价: 0.0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_in.py:801]
2025-06-03 13:16:55,283 INFO: 批次编辑器 - 入库单ID: 93 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_in.py:799]
2025-06-03 13:16:55,289 INFO: 项目ID: 207, 食材: 米饭, 批次号: B20250603b9b7cb, 单价: 0.0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_in.py:801]
