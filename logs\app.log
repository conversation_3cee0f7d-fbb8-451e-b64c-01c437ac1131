2025-06-03 13:32:57,608 INFO: 使用库存记录: ID=70, 批次号=B20250602379197, 数量=199.70 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:895]
2025-06-03 13:32:57,611 INFO: 准备更新库存: ID=70, 减少数量=199.7 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:916]
2025-06-03 13:32:57,612 INFO: 使用库存记录: ID=71, 批次号=B202506027a72b5, 数量=100.00 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:895]
2025-06-03 13:32:57,612 INFO: 准备更新库存: ID=71, 减少数量=100.0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:916]
2025-06-03 13:32:57,617 INFO: 使用库存记录: ID=72, 批次号=B202506021cfe48, 数量=200.00 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:895]
2025-06-03 13:32:57,617 INFO: 准备更新库存: ID=72, 减少数量=200.0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:916]
2025-06-03 13:32:57,618 INFO: 使用库存记录: ID=73, 批次号=B2025060241bb6d, 数量=100.00 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:895]
2025-06-03 13:32:57,618 INFO: 准备更新库存: ID=73, 减少数量=100.0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:916]
2025-06-03 13:32:57,618 INFO: 使用库存记录: ID=74, 批次号=B20250602d76ba9, 数量=699.80 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:895]
2025-06-03 13:32:57,619 INFO: 准备更新库存: ID=74, 减少数量=699.8 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:916]
2025-06-03 13:32:57,619 WARNING: 库存不足: 食材=米饭, 需要=1000.0, 当前=699.8 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:826]
2025-06-03 13:32:57,622 INFO: 创建平账入库: 食材=米饭, 需要=1000.0, 当前=699.8, 差额=300.20000000000005, 批次号=BAL-20250603-72-6b9357 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\balance_stock_in.py:53]
2025-06-03 13:32:57,639 INFO: 创建新库存记录: 批次号=BAL-20250603-72-6b9357, 数量=300.20000000000005 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\balance_stock_in.py:170]
2025-06-03 13:32:57,647 INFO: 平账入库完成: 食材=米饭, 补充数量=300.20000000000005, 入库单ID=94 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\balance_stock_in.py:200]
2025-06-03 13:32:57,647 INFO: 平账入库成功: 食材=米饭, 补充数量=300.20000000000005, 批次号=BAL-20250603-72-6b9357 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:844]
2025-06-03 13:32:57,651 INFO: 平账入库后找到 1 条库存记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:889]
2025-06-03 13:32:57,651 INFO: 使用库存记录: ID=76, 批次号=BAL-20250603-72-6b9357, 数量=300.20 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:895]
2025-06-03 13:32:57,652 INFO: 准备更新库存: ID=76, 减少数量=1000.0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:916]
2025-06-03 13:32:57,657 INFO: 执行库存更新: 共 13 条记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:945]
2025-06-03 13:32:57,767 INFO: 当前用户: 18373062333 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:94]
2025-06-03 13:32:57,768 INFO: 用户区域ID: 42 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:95]
2025-06-03 13:32:57,769 INFO: 用户区域名称: 朝阳区实验中学 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:96]
2025-06-03 13:32:57,770 INFO: 是否管理员: 0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:97]
