2025-06-03 17:22:59,467 ERROR: 库存统计查询失败: (pyodbc.ProgrammingError) ('42000', "[42000] [Microsoft][ODBC SQL Server Driver][SQL Server]'DATE' 不是可以识别的 内置函数名称。 (195) (SQLExecDirectW); [42000] [Microsoft][ODBC SQL Server Driver][SQL Server]无法预定义语句。 (8180)")
[SQL: 
        SELECT
            DATE(si.stock_in_date) as date,
            COUNT(DISTINCT si.id) as stock_in_count,
            COUNT(sii.id) as stock_in_items,
            SUM(sii.quantity * sii.unit_price) as stock_in_amount,
            SUM(sii.quantity) as stock_in_quantity
        FROM stock_ins si
        JOIN stock_in_items sii ON si.id = sii.stock_in_id
        JOIN warehouses w ON si.warehouse_id = w.id
        JOIN ingredients i ON sii.ingredient_id = i.id
        WHERE w.area_id = 42 AND DATE(si.stock_in_date) >= ? AND DATE(si.stock_in_date) <= ?
        GROUP BY DATE(si.stock_in_date)
        ORDER BY DATE(si.stock_in_date)
    ]
[parameters: ('2025-05-04', '2025-06-03')]
(Background on this error at: https://sqlalche.me/e/20/f405) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:948]
2025-06-03 17:23:08,807 ERROR: 库存统计查询失败: (pyodbc.ProgrammingError) ('42000', "[42000] [Microsoft][ODBC SQL Server Driver][SQL Server]'DATE' 不是可以识别的 内置函数名称。 (195) (SQLExecDirectW); [42000] [Microsoft][ODBC SQL Server Driver][SQL Server]无法预定义语句。 (8180)")
[SQL: 
        SELECT
            DATE(si.stock_in_date) as date,
            COUNT(DISTINCT si.id) as stock_in_count,
            COUNT(sii.id) as stock_in_items,
            SUM(sii.quantity * sii.unit_price) as stock_in_amount,
            SUM(sii.quantity) as stock_in_quantity
        FROM stock_ins si
        JOIN stock_in_items sii ON si.id = sii.stock_in_id
        JOIN warehouses w ON si.warehouse_id = w.id
        JOIN ingredients i ON sii.ingredient_id = i.id
        WHERE w.area_id = 42 AND DATE(si.stock_in_date) >= ? AND DATE(si.stock_in_date) <= ? AND sii.storage_location_id = ?
        GROUP BY DATE(si.stock_in_date)
        ORDER BY DATE(si.stock_in_date)
    ]
[parameters: ('2025-05-04', '2025-06-03', 9)]
(Background on this error at: https://sqlalche.me/e/20/f405) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:948]
2025-06-03 17:23:36,726 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:786]
2025-06-03 17:24:16,914 WARNING: Suspicious method blocked: HEAD from 14.103.175.118 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:120]
2025-06-03 17:24:16,915 ERROR: Security middleware error: 405 Method Not Allowed: The method is not allowed for the requested URL. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:161]
2025-06-03 17:24:17,685 ERROR: 库存统计查询失败: (pyodbc.ProgrammingError) ('42000', "[42000] [Microsoft][ODBC SQL Server Driver][SQL Server]'DATE' 不是可以识别的 内置函数名称。 (195) (SQLExecDirectW); [42000] [Microsoft][ODBC SQL Server Driver][SQL Server]无法预定义语句。 (8180)")
[SQL: 
        SELECT
            DATE(si.stock_in_date) as date,
            COUNT(DISTINCT si.id) as stock_in_count,
            COUNT(sii.id) as stock_in_items,
            SUM(sii.quantity * sii.unit_price) as stock_in_amount,
            SUM(sii.quantity) as stock_in_quantity
        FROM stock_ins si
        JOIN stock_in_items sii ON si.id = sii.stock_in_id
        JOIN warehouses w ON si.warehouse_id = w.id
        JOIN ingredients i ON sii.ingredient_id = i.id
        WHERE w.area_id = 42 AND DATE(si.stock_in_date) >= ? AND DATE(si.stock_in_date) <= ?
        GROUP BY DATE(si.stock_in_date)
        ORDER BY DATE(si.stock_in_date)
    ]
[parameters: ('2025-05-04', '2025-06-03')]
(Background on this error at: https://sqlalche.me/e/20/f405) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:948]
2025-06-03 17:24:21,442 ERROR: 库存统计查询失败: (pyodbc.ProgrammingError) ('42000', "[42000] [Microsoft][ODBC SQL Server Driver][SQL Server]'DATE' 不是可以识别的 内置函数名称。 (195) (SQLExecDirectW); [42000] [Microsoft][ODBC SQL Server Driver][SQL Server]无法预定义语句。 (8180)")
[SQL: 
        SELECT
            DATE(si.stock_in_date) as date,
            COUNT(DISTINCT si.id) as stock_in_count,
            COUNT(sii.id) as stock_in_items,
            SUM(sii.quantity * sii.unit_price) as stock_in_amount,
            SUM(sii.quantity) as stock_in_quantity
        FROM stock_ins si
        JOIN stock_in_items sii ON si.id = sii.stock_in_id
        JOIN warehouses w ON si.warehouse_id = w.id
        JOIN ingredients i ON sii.ingredient_id = i.id
        WHERE w.area_id = 42 AND DATE(si.stock_in_date) >= ? AND DATE(si.stock_in_date) <= ?
        GROUP BY DATE(si.stock_in_date)
        ORDER BY DATE(si.stock_in_date)
    ]
[parameters: ('2025-05-04', '2025-06-03')]
(Background on this error at: https://sqlalche.me/e/20/f405) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:948]
2025-06-03 17:24:23,404 ERROR: 库存统计查询失败: (pyodbc.ProgrammingError) ('42000', "[42000] [Microsoft][ODBC SQL Server Driver][SQL Server]'DATE' 不是可以识别的 内置函数名称。 (195) (SQLExecDirectW); [42000] [Microsoft][ODBC SQL Server Driver][SQL Server]无法预定义语句。 (8180)")
[SQL: 
        SELECT
            DATE(si.stock_in_date) as date,
            COUNT(DISTINCT si.id) as stock_in_count,
            COUNT(sii.id) as stock_in_items,
            SUM(sii.quantity * sii.unit_price) as stock_in_amount,
            SUM(sii.quantity) as stock_in_quantity
        FROM stock_ins si
        JOIN stock_in_items sii ON si.id = sii.stock_in_id
        JOIN warehouses w ON si.warehouse_id = w.id
        JOIN ingredients i ON sii.ingredient_id = i.id
        WHERE w.area_id = 42 AND DATE(si.stock_in_date) >= ? AND DATE(si.stock_in_date) <= ?
        GROUP BY DATE(si.stock_in_date)
        ORDER BY DATE(si.stock_in_date)
    ]
[parameters: ('2025-05-04', '2025-06-03')]
(Background on this error at: https://sqlalche.me/e/20/f405) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\inventory.py:948]
